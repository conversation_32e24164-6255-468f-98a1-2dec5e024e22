const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const vscode = require('vscode');

/**
 * AI Agent class for intelligent coding assistance
 */
class AIAgent {
    constructor(mcpTools) {
        this.mcpTools = mcpTools;
        this.apiKey = this.getApiKey();
        this.model = this.getModel();
        this.baseURL = 'https://api.groq.com/openai/v1/chat/completions';
        this.conversationHistory = [];
        this.currentProject = null; // Track current project
        this.projectFiles = []; // Track files to create
        this.currentFileIndex = 0; // Track current file being created

        // Enhanced error handling and retry system
        this.lastRequestTime = 0;
        this.minRequestInterval = 2000; // 2 seconds minimum between requests
        this.maxRetries = 5; // Increased to 5 retries
        this.baseRetryDelay = 5000; // 5 seconds base delay
        this.maxRetryDelay = 60000; // 60 seconds max delay
        this.requestQueue = [];
        this.isProcessingQueue = false;
        this.consecutiveErrors = 0;
        this.lastErrorTime = 0;



        // Test API connection on initialization
        this.testApiConnection();
    }

    /**
     * Test API connection
     */
    async testApiConnection() {
        try {


            const testMessages = [{
                role: 'user',
                content: 'Hello'
            }];

            const response = await axios.post(this.baseURL, {
                model: this.model,
                messages: testMessages,
                max_tokens: 10,
                temperature: 0.1
            }, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000
            });

            if (response.status === 200) {

            }

        } catch (error) {
            const errorInfo = this.parseApiError(error);
            console.error('❌ API connection test failed:', errorInfo.message);
        }
    }

    /**
     * Get API key from configuration or use default
     */
    getApiKey() {
        const config = vscode.workspace.getConfiguration('augura');
        const userApiKey = config.get('groqApiKey');

        // Use user's API key if provided, otherwise use default
        const apiKey = userApiKey || '********************************************************';

        // Basic validation of API key format
        if (!apiKey || typeof apiKey !== 'string' || apiKey.length < 10) {
            console.error('❌ Invalid API key format.');
            throw new Error('Invalid API key format.');
        }

        return apiKey;
    }

    /**
     * Get model from configuration
     */
    getModel() {
        const config = vscode.workspace.getConfiguration('augura');
        const model = config.get('model') || 'moonshotai/kimi-k2-instruct';

        // List of supported models
        const supportedModels = [
            'moonshotai/kimi-k2-instruct',
            'llama3-8b-8192',
            'llama3-70b-8192',
            'mixtral-8x7b-32768',
            'gemma-7b-it'
        ];

        if (!supportedModels.includes(model)) {
            console.warn(`⚠️ Model "${model}" may not be supported. Supported models:`, supportedModels);
        }

        return model;
    }

    /**
     * Process user message with full agent capabilities and rate limiting
     */
    async processMessage(userMessage, context) {
        const messageId = uuidv4();

        // Add to queue for rate limiting
        return new Promise((resolve, reject) => {
            this.requestQueue.push({
                messageId,
                userMessage,
                context,
                resolve,
                reject,
                timestamp: Date.now()
            });

            this.processQueue();
        });
    }

    /**
     * Process the request queue with rate limiting
     */
    async processQueue() {
        if (this.isProcessingQueue || this.requestQueue.length === 0) {
            return;
        }

        this.isProcessingQueue = true;

        while (this.requestQueue.length > 0) {
            const request = this.requestQueue.shift();

            try {
                // Check rate limiting
                const timeSinceLastRequest = Date.now() - this.lastRequestTime;
                if (timeSinceLastRequest < this.minRequestInterval) {
                    const waitTime = this.minRequestInterval - timeSinceLastRequest;
                    await this.sleep(waitTime);
                }

                // Process the actual request
                const result = await this.processMessageInternal(
                    request.userMessage,
                    request.context,
                    request.messageId
                );

                this.lastRequestTime = Date.now();
                request.resolve(result);

            } catch (error) {
                // Use enhanced error handling
                await this.handleApiError(request, error);
            }
        }

        this.isProcessingQueue = false;
    }

    /**
     * Internal message processing without rate limiting
     */
    async processMessageInternal(userMessage, context, messageId) {

        // Validate user message
        if (!userMessage || typeof userMessage !== 'string' || userMessage.trim().length === 0) {
            throw new Error('Invalid user message: message cannot be empty');
        }

        try {
            // Add user message to history with unique ID
            const userMessageId = `user-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
            this.conversationHistory.push({
                role: 'user',
                content: userMessage,
                messageId: userMessageId,
                timestamp: new Date().toISOString()
            });

            // Create system prompt for agent mode
            const systemPrompt = this.createAgentSystemPrompt(context);

            // Prepare messages for API
            const messages = [
                { role: 'system', content: systemPrompt },
                ...this.conversationHistory.slice(-10) // Keep last 10 messages for context
            ];

            // Check if streaming is enabled
            const config = vscode.workspace.getConfiguration('ai-agent');
            const streamEnabled = config.get('streamResponse', true);
            if (streamEnabled) {
                try {
                    return await this.streamResponse(messages, messageId);
                } catch (streamError) {
                    console.warn('⚠️ Streaming failed, falling back to complete response');
                    return await this.getCompleteResponse(messages, messageId);
                }
            } else {
                return await this.getCompleteResponse(messages, messageId);
            }

        } catch (error) {
            console.error('❌ Error processing message internally:', error);
            throw new Error(`Failed to process message: ${error.message}`);
        }
    }

    /**
     * Create system prompt for agent mode
     */
    createAgentSystemPrompt(context) {
        // Check if we're in continuation mode
        if (this.currentProject && this.currentFileIndex < this.projectFiles.length) {
            const currentFile = this.projectFiles[this.currentFileIndex];
            return `You are continuing to create files for the project: "${this.currentProject.name}"

CURRENT TASK: Create the file "${currentFile.name}" (${this.currentFileIndex + 1}/${this.projectFiles.length})

File description: ${currentFile.description}

CRITICAL: Create ONLY this ONE file using this EXACT format:

**File: ${currentFile.name}**
\`\`\`${currentFile.language}
[Complete ${currentFile.language} code for ${currentFile.name}]
\`\`\`

RULES:
1. Create COMPLETE, working code for this file
2. Make it production-ready
3. Include all necessary content
4. Don't mention other files - focus only on this one
5. After the code block, you can naturally comment about the file or what you've created

WORKSPACE CONTEXT:
${context ? JSON.stringify(context, null, 2) : 'No workspace context available'}`;
        }

        // Initial project planning mode
        return `You are an intelligent AI coding agent with direct access to VS Code file system.

CRITICAL: When user asks to create files/projects, you MUST create a PROJECT PLAN first:

🎯 **Project Plan: [Project Name]**

I'll create this project with the following files:
1. **index.html** - Main HTML structure
2. **style.css** - Styling and layout
3. **script.js** - JavaScript functionality

Let me start with the first file:

**File: index.html**
\`\`\`html
[Complete HTML code]
\`\`\`

WORKSPACE CONTEXT:
${context ? JSON.stringify(context, null, 2) : 'No workspace context available'}

RULES:
1. ALWAYS create a project plan first
2. Create ONE file at a time to avoid token limits
3. Use "**File: filename.ext**" before each code block
4. After each file, you can naturally comment about what you've created
5. Make each file complete and production-ready
6. Focus on ONE file per response

IMPORTANT: Due to token limits, create only ONE file per response.`;
    }

    /**
     * Stream response from Groq API
     */
    async streamResponse(messages, messageId) {

        // Validate messages before sending
        const validatedMessages = this.validateMessages(messages);

        try {
            const requestData = {
                model: this.model,
                messages: validatedMessages,
                stream: true,
                temperature: 0.7
            };


            const response = await axios.post(this.baseURL, requestData, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                responseType: 'stream',
                timeout: 30000 // 30 second timeout
            });

            return {
                messageId,
                stream: this.parseStreamResponse(response.data, messageId)
            };

        } catch (error) {
            const errorInfo = this.parseApiError(error);
            throw new Error(errorInfo.message);
        }
    }

    /**
     * Validate and clean messages before sending to API
     */
    validateMessages(messages) {
        const validatedMessages = [];

        for (const message of messages) {
            // Skip invalid messages
            if (!message || typeof message !== 'object') {
                console.warn('⚠️ Skipping invalid message:', message);
                continue;
            }

            // Ensure required fields exist
            if (!message.role || !message.content) {
                console.warn('⚠️ Skipping message with missing role or content:', message);
                continue;
            }

            // Validate role
            if (!['system', 'user', 'assistant'].includes(message.role)) {
                console.warn('⚠️ Invalid role, defaulting to user:', message.role);
                message.role = 'user';
            }

            // Clean and validate content
            let content = message.content;
            if (typeof content !== 'string') {
                content = String(content);
            }

            // Remove null characters and other problematic characters
            content = content.replace(/\0/g, '').replace(/\x00/g, '');

            // Trim excessive whitespace
            content = content.trim();

            // Skip empty messages
            if (!content) {
                console.warn('⚠️ Skipping empty message');
                continue;
            }

            // Limit message length (Groq has token limits)
            if (content.length > 10000) {
                content = content.substring(0, 10000) + '... [truncated]';
                console.warn('⚠️ Message truncated due to length');
            }

            validatedMessages.push({
                role: message.role,
                content: content
            });
        }

        // Ensure we have at least one message
        if (validatedMessages.length === 0) {
            console.warn('⚠️ No valid messages found, adding default message');
            validatedMessages.push({
                role: 'user',
                content: 'Hello'
            });
        }

        return validatedMessages;
    }

    /**
     * Parse streaming response
     */
    async* parseStreamResponse(stream, messageId) {
        let buffer = '';
        let fullResponse = '';
        let pendingFileContent = '';
        let currentFileName = null;
        let currentLanguage = null;
        let inCodeBlock = false;

        for await (const chunk of stream) {
            buffer += chunk.toString();
            const lines = buffer.split('\n');
            buffer = lines.pop(); // Keep incomplete line in buffer

            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    const data = line.slice(6);
                    if (data === '[DONE]') {
                        // Process any remaining file content
                        if (currentFileName && pendingFileContent) {
                            await this.createFileImmediately(currentFileName, pendingFileContent);
                        }

                        // Process complete response for any missed files
                        await this.processFileOperations(fullResponse);

                        // Add complete response to history with messageId and timestamp
                        this.conversationHistory.push({
                            role: 'assistant',
                            content: fullResponse,
                            messageId: messageId,
                            timestamp: new Date().toISOString()
                        });
                        return;
                    }

                    try {
                        const parsed = JSON.parse(data);
                        const content = parsed.choices?.[0]?.delta?.content;
                        if (content) {
                            fullResponse += content;

                            // Check for file creation patterns in real-time
                            await this.processStreamingContent(content, {
                                pendingFileContent,
                                currentFileName,
                                currentLanguage,
                                inCodeBlock
                            });

                            yield content;
                        }
                    } catch (e) {
                        // Ignore parsing errors for malformed chunks
                    }
                }
            }
        }
    }

    /**
     * Get complete response (non-streaming)
     */
    async getCompleteResponse(messages, messageId) {

        // Validate messages before sending
        const validatedMessages = this.validateMessages(messages);

        try {
            const requestData = {
                model: this.model,
                messages: validatedMessages,
                temperature: 0.7,
                max_tokens: 4096
            };

            const response = await axios.post(this.baseURL, requestData, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 30000 // 30 second timeout
            });

            const content = response.data.choices[0].message.content;

            // Process file operations
            await this.processFileOperations(content);

            // Add response to history with messageId and timestamp
            this.conversationHistory.push({
                role: 'assistant',
                content: content,
                messageId: messageId,
                timestamp: new Date().toISOString()
            });

            return {
                messageId,
                content: content
            };

        } catch (error) {
            const errorInfo = this.parseApiError(error);
            throw new Error(errorInfo.message);
        }
    }


    /**
     * Generate code from description
     */
    async generateCode(description, language) {
        const prompt = `Generate ${language} code for: ${description}

Requirements:
- Write clean, well-commented code
- Follow best practices for ${language}
- Include error handling where appropriate
- Make the code production-ready

Return ONLY the code without explanations.`;

        const messages = [
            { role: 'system', content: `You are an expert ${language} developer. Generate high-quality code.` },
            { role: 'user', content: prompt }
        ];

        const response = await this.getCompleteResponse(messages, uuidv4());

        // Extract code from response
        let generatedCode = response.content;
        const codeBlockRegex = new RegExp(`\`\`\`${language}\\n([\\s\\S]*?)\\n\`\`\``, 'i');
        const match = generatedCode.match(codeBlockRegex);
        if (match) {
            generatedCode = match[1];
        }

        return generatedCode.trim();
    }


    /**
     * Quick analysis for auto-analyze feature
     */
    async quickAnalyze(code, language) {
        // Simplified analysis for background processing
        if (code.length > 5000) return; // Skip very large files

        try {
            const issues = await this.findQuickIssues(code, language);
            if (issues.length > 0) {
                // Could show subtle indicators in VS Code
            }
        } catch (error) {
            console.error('Quick analyze error:', error);
        }
    }

    /**
     * Find quick issues in code
     */
    async findQuickIssues(code, language) {
        // Simple pattern-based issue detection
        const issues = [];

        // Common issues to check
        if (code.includes('console.log') && language === 'javascript') {
            issues.push('Debug console.log statements found');
        }

        if (code.includes('TODO') || code.includes('FIXME')) {
            issues.push('TODO/FIXME comments found');
        }

        return issues;
    }

    /**
     * Process file operations from AI response
     */
    async processFileOperations(response) {

        try {
            // Check if this is a project plan
            const projectPlanMatch = response.match(/🎯\s*\*\*Project Plan:\s*([^*]+)\*\*/);
            if (projectPlanMatch) {
                const projectName = projectPlanMatch[1].trim();
                await this.initializeProject(projectName, response);
            }

            // Look for new format: **File: filename.ext**
            const newFormatMatches = response.match(/\*\*File:\s*([^*]+)\*\*\s*\n```(\w+)\n([\s\S]*?)```/g);
            if (newFormatMatches) {
                for (const match of newFormatMatches) {
                    const [, filename, , content] = match.match(/\*\*File:\s*([^*]+)\*\*\s*\n```(\w+)\n([\s\S]*?)```/);
                    const cleanFilename = filename.trim();

                    const result = await this.mcpTools.createFile(cleanFilename, content.trim());
                    if (result.success) {

                        // Update project progress
                        if (this.currentProject) {
                            this.currentFileIndex++;
                            // Update files tree in sidebar
                            this.updateFilesTree();
                        }
                    } else {
                        console.error(`❌ Failed to create file: ${result.error}`);
                    }
                }

                // Project file created successfully
                return; // Exit early if we found the new format
            }

            // Fallback: Look for old FILE_CREATE commands
            const createMatches = response.match(/FILE_CREATE:\s*([^\n]+)\n```(\w+)\n([\s\S]*?)```/g);
            if (createMatches) {
                for (const match of createMatches) {
                    const [, filename, , content] = match.match(/FILE_CREATE:\s*([^\n]+)\n```(\w+)\n([\s\S]*?)```/);

                    const result = await this.mcpTools.createFile(filename.trim(), content.trim());
                    if (result.success) {
                        // File created (tracking removed)
                    } else {
                        console.error(`❌ Failed to create file: ${result.error}`);
                    }
                }
                return; // Exit early if we found FILE_CREATE commands
            }

            // Fallback: Look for FILE_WRITE commands
            const writeMatches = response.match(/FILE_WRITE:\s*([^\n]+)\n```(\w+)\n([\s\S]*?)```/g);
            if (writeMatches) {
                for (const match of writeMatches) {
                    const [, filename, , content] = match.match(/FILE_WRITE:\s*([^\n]+)\n```(\w+)\n([\s\S]*?)```/);

                    const result = await this.mcpTools.writeFile(filename.trim(), content.trim());
                    if (result.success) {
                        // File modified (tracking removed)
                    } else {
                        console.error(`❌ Failed to write file: ${result.error}`);
                    }
                }
                return; // Exit early if we found FILE_WRITE commands
            }

            // Look for standard code blocks that should be saved as files
            const codeBlocks = response.match(/```(\w+)\n([\s\S]*?)```/g);
            if (codeBlocks && codeBlocks.length > 0) {
                // Check if response mentions creating files
                const mentionsFiles = /create|make|build|generate.*file|website|project/i.test(response);

                if (mentionsFiles) {
                    for (let i = 0; i < codeBlocks.length; i++) {
                        const [, language, content] = codeBlocks[i].match(/```(\w+)\n([\s\S]*?)```/);

                        // Try to determine filename from context or language
                        let filename = this.guessFilename(content, language, i);

                        const result = await this.mcpTools.createFile(filename, content.trim());

                        if (result.success) {
                            // File auto-created (tracking removed)
                        } else {
                            console.error(`❌ Failed to auto-create file: ${result.error}`);
                        }
                    }
                }
            }

        } catch (error) {
            console.error('❌ Error processing file operations:', error);
        }
    }

    /**
     * Guess filename from code content and language
     */
    guessFilename(content, language, index = 0) {
        // Try to extract filename from comments
        const filenameComment = content.match(/(?:\/\/|\/\*|<!--|\#)\s*(?:filename|file):\s*([^\s\n\*]+)/i);
        if (filenameComment) {
            return filenameComment[1];
        }

        // Check for HTML
        if (language === 'html' || content.includes('<!DOCTYPE html>')) {
            if (content.includes('<title>') || index === 0) {
                return 'index.html';
            }
            return `page${index + 1}.html`;
        }

        // Check for CSS
        if (language === 'css' || content.includes('body {') || content.includes('@media')) {
            return index === 0 ? 'style.css' : `style${index + 1}.css`;
        }

        // Check for JavaScript
        if (language === 'javascript' || language === 'js') {
            if (content.includes('document.') || content.includes('window.')) {
                return index === 0 ? 'script.js' : `script${index + 1}.js`;
            }
            return index === 0 ? 'main.js' : `file${index + 1}.js`;
        }

        // Check for Python
        if (language === 'python' || language === 'py') {
            return index === 0 ? 'main.py' : `file${index + 1}.py`;
        }

        // Default extensions
        const extensions = {
            'html': 'html',
            'css': 'css',
            'javascript': 'js',
            'js': 'js',
            'python': 'py',
            'py': 'py',
            'java': 'java',
            'cpp': 'cpp',
            'c': 'c',
            'php': 'php',
            'ruby': 'rb',
            'go': 'go',
            'rust': 'rs',
            'typescript': 'ts',
            'ts': 'ts'
        };

        const ext = extensions[language] || 'txt';
        return index === 0 ? `main.${ext}` : `file${index + 1}.${ext}`;
    }

    /**
     * Process streaming content for immediate file creation
     */
    async processStreamingContent() {
        // This is a simplified version - in practice you'd need more sophisticated parsing
        // For now, we'll rely on the complete response processing
        return;
    }

    /**
     * Create file immediately during streaming
     */
    async createFileImmediately(filename, content) {
        try {
            const result = await this.mcpTools.createFile(filename, content);
            if (result.success) {
                // File created immediately (tracking removed)
            } else {
                console.error(`❌ Failed to create file immediately: ${result.error}`);
            }
        } catch (error) {
            console.error(`❌ Error creating file immediately: ${error.message}`);
        }
    }

    /**
     * Update files tree in sidebar
     */
    updateFilesTree() {
        try {
            // This would need access to the extension context
            // For now, we'll use a global reference if available
            if (global.filesTreeProvider) {
                const createdFiles = this.projectFiles.slice(0, this.currentFileIndex).map(file => ({
                    name: file.name,
                    description: file.description,
                    path: file.name // This would be the full path in real implementation
                }));

                global.filesTreeProvider.updateProjectFiles(createdFiles);
            }
        } catch (error) {
            console.error('Error updating files tree:', error);
        }
    }







    /**
     * Initialize a new project from the plan
     */
    async initializeProject(projectName, response) {

        this.currentProject = {
            name: projectName,
            startTime: new Date()
        };

        // Extract file list from project plan
        this.projectFiles = this.extractProjectFiles(response);
        this.currentFileIndex = 0;

    }

    /**
     * Extract project files from the response
     */
    extractProjectFiles(response) {
        const files = [];

        // Look for numbered list of files
        const fileListMatches = response.match(/\d+\.\s*\*\*([^*]+)\*\*\s*-\s*([^\n]+)/g);
        if (fileListMatches) {
            fileListMatches.forEach(match => {
                const [, filename, description] = match.match(/\d+\.\s*\*\*([^*]+)\*\*\s*-\s*([^\n]+)/);
                const ext = filename.split('.').pop().toLowerCase();
                const language = this.getLanguageFromExtension(ext);

                files.push({
                    name: filename.trim(),
                    description: description.trim(),
                    language: language
                });
            });
        }

        // Fallback: common web files
        if (files.length === 0) {
            files.push(
                { name: 'index.html', description: 'Main HTML structure', language: 'html' },
                { name: 'style.css', description: 'Styling and layout', language: 'css' },
                { name: 'script.js', description: 'JavaScript functionality', language: 'javascript' }
            );
        }

        return files;
    }

    /**
     * Get programming language from file extension
     */
    getLanguageFromExtension(ext) {
        const languageMap = {
            'html': 'html',
            'css': 'css',
            'js': 'javascript',
            'py': 'python',
            'java': 'java',
            'cpp': 'cpp',
            'c': 'c',
            'php': 'php',
            'rb': 'ruby',
            'go': 'go',
            'rs': 'rust',
            'ts': 'typescript'
        };

        return languageMap[ext] || 'text';
    }







    /**
     * Enhanced error handling with detailed error messages and smart retry logic
     */
    async handleApiError(request, error) {
        if (!request.retryCount) {
            request.retryCount = 0;
        }

        const errorInfo = this.parseApiError(error);

        // Check if error is retryable
        if (!errorInfo.retryable) {
            // Show error message to user for non-retryable errors
            this.showErrorToUser(errorInfo);
            request.reject(new Error(errorInfo.message));
            return;
        }

        // Check retry limit
        if (request.retryCount >= this.maxRetries) {
            const finalMessage = `${errorInfo.message}\n\nFailed after ${this.maxRetries} retry attempts. Please try again later.`;
            this.showErrorToUser({
                ...errorInfo,
                message: finalMessage
            });
            request.reject(new Error(finalMessage));
            return;
        }

        // Calculate smart retry delay
        const delay = this.calculateRetryDelay(request.retryCount, errorInfo.errorType);
        request.retryCount++;

        // Show retry notification to user
        this.notifyRetryAttempt(request.retryCount, this.maxRetries, delay, errorInfo.errorType);

        // Wait for the delay
        await this.sleep(delay);

        // Add back to front of queue for immediate processing
        this.requestQueue.unshift(request);
    }

    /**
     * Parse API error and return detailed error information
     */
    parseApiError(error) {
        const status = error.response?.status;
        const statusText = error.response?.statusText;
        const responseData = error.response?.data;

        let errorType = 'unknown';
        let message = 'An unexpected error occurred';
        let retryable = false;

        switch (status) {
            case 400:
                errorType = 'bad_request';
                message = 'Bad Request: Invalid request format or parameters. Please check your input and try again.';
                retryable = false;
                break;

            case 401:
                errorType = 'unauthorized';
                message = 'Unauthorized: Invalid API key. Please check your API key configuration in VS Code settings.';
                retryable = false;
                break;

            case 403:
                errorType = 'forbidden';
                message = 'Forbidden: Access denied. Your API key may not have permission for this operation.';
                retryable = false;
                break;

            case 404:
                errorType = 'not_found';
                message = 'Not Found: The requested model or endpoint is not available. Please check your model configuration.';
                retryable = false;
                break;

            case 413:
                errorType = 'payload_too_large';
                message = 'Payload Too Large: Your message is too long. Please try with a shorter message.';
                retryable = false;
                break;

            case 429:
                errorType = 'rate_limit';
                message = 'Rate Limit Exceeded: Too many requests. Retrying automatically...';
                retryable = true;
                break;

            case 500:
                errorType = 'server_error';
                message = 'Internal Server Error: The AI service is experiencing issues. Retrying automatically...';
                retryable = true;
                break;

            case 502:
                errorType = 'bad_gateway';
                message = 'Bad Gateway: Service temporarily unavailable. Retrying automatically...';
                retryable = true;
                break;

            case 503:
                errorType = 'service_unavailable';
                message = 'Service Unavailable: The AI service is temporarily down. Retrying automatically...';
                retryable = true;
                break;

            case 504:
                errorType = 'gateway_timeout';
                message = 'Gateway Timeout: Request timed out. Retrying automatically...';
                retryable = true;
                break;

            default:
                if (error.code === 'ECONNREFUSED') {
                    errorType = 'connection_refused';
                    message = 'Connection Refused: Unable to connect to the AI service. Please check your internet connection.';
                    retryable = true;
                } else if (error.code === 'ENOTFOUND') {
                    errorType = 'dns_error';
                    message = 'DNS Error: Unable to resolve the AI service address. Please check your internet connection.';
                    retryable = true;
                } else if (error.code === 'ETIMEDOUT') {
                    errorType = 'timeout';
                    message = 'Request Timeout: The request took too long to complete. Retrying automatically...';
                    retryable = true;
                } else {
                    errorType = 'unknown';
                    message = `Unknown Error: ${error.message || 'An unexpected error occurred'}`;
                    retryable = true; // Give unknown errors a chance to retry
                }
        }

        // Add specific error details if available
        if (responseData?.error?.message) {
            message += `\n\nDetails: ${responseData.error.message}`;
        }

        return {
            errorType,
            message,
            retryable,
            status,
            statusText
        };
    }

    /**
     * Calculate smart retry delay with exponential backoff
     */
    calculateRetryDelay(retryCount, errorType) {
        let baseDelay = this.baseRetryDelay;

        // Different base delays for different error types
        switch (errorType) {
            case 'rate_limit':
                baseDelay = 10000; // 10 seconds for rate limits
                break;
            case 'server_error':
            case 'bad_gateway':
            case 'service_unavailable':
                baseDelay = 15000; // 15 seconds for server issues
                break;
            case 'gateway_timeout':
            case 'timeout':
                baseDelay = 8000; // 8 seconds for timeouts
                break;
            default:
                baseDelay = this.baseRetryDelay; // 5 seconds default
        }

        // Exponential backoff with jitter
        const exponentialDelay = baseDelay * Math.pow(2, retryCount);
        const jitter = Math.random() * 1000; // Add up to 1 second of jitter
        const finalDelay = Math.min(exponentialDelay + jitter, this.maxRetryDelay);

        return Math.floor(finalDelay);
    }

    /**
     * Notify user about retry attempt
     */
    notifyRetryAttempt(currentAttempt, maxAttempts, delayMs, errorType) {
        const delaySeconds = Math.ceil(delayMs / 1000);
        const errorTypeText = errorType.replace('_', ' ').toUpperCase();

        // Send notification to webview
        if (global.chatWebviewProvider) {
            global.chatWebviewProvider.postMessage({
                type: 'retryNotification',
                attempt: currentAttempt,
                maxAttempts: maxAttempts,
                delay: delaySeconds,
                errorType: errorTypeText
            });
        }
    }

    /**
     * Show error message to user
     */
    showErrorToUser(errorInfo) {
        // Send error notification to webview
        if (global.chatWebviewProvider) {
            global.chatWebviewProvider.postMessage({
                type: 'errorNotification',
                errorType: errorInfo.errorType,
                message: errorInfo.message,
                status: errorInfo.status,
                retryable: errorInfo.retryable
            });
        }
    }

    /**
     * Sleep utility function
     */
    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Get adaptive delay based on current queue size and recent errors
     */
    getAdaptiveDelay() {
        const baseDelay = this.minRequestInterval;
        const queueMultiplier = Math.min(this.requestQueue.length * 1000, 10000); // Max 10s extra
        const adaptiveDelay = baseDelay + queueMultiplier;

        return adaptiveDelay;
    }

    /**
     * Check queue health and provide status
     */
    getQueueStatus() {
        return {
            queueLength: this.requestQueue.length,
            isProcessing: this.isProcessingQueue,
            timeSinceLastRequest: Date.now() - this.lastRequestTime,
            minInterval: this.minRequestInterval
        };
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        this.conversationHistory = [];
        this.currentProject = null;
        this.projectFiles = [];
        this.currentFileIndex = 0;
        this.requestQueue = [];
        this.isProcessingQueue = false;
    }
}

module.exports = { AIAgent };
