const vscode = require('vscode');
const path = require('path');
const fs = require('fs');
const { AIAgent } = require('./utils/ai-agent');
const { MCPTools } = require('./utils/mcp-tools');

// Conversations storage paths
let conversationsDir;
let conversationsIndexPath;

let aiAgent;
let mcpTools;
let chatWebviewProvider;
let filesTreeProvider;

// Conversations management
let conversations = new Map(); // threadId -> conversation data
let currentThreadId = null;
let conversationCounter = 0;
let lastSavedMessageCount = 0; // Track last saved message count to avoid unnecessary saves

/**
 * Extension activation function
 */
async function activate(context) {
    try {
        // Initialize MCP Tools and AI Agent
        mcpTools = new MCPTools();
        aiAgent = new AIAgent(mcpTools);

        // Register commands
        registerCommands(context);

        // Register webview providers for sidebar
        registerWebviewProviders(context);

        // Auto-analyze code when opening files (if enabled)
        setupAutoAnalyze(context);

        // Initialize conversations system
        conversationsDir = path.join(context.globalStorageUri.fsPath, 'conversations');
        conversationsIndexPath = path.join(context.globalStorageUri.fsPath, 'index.json');
        await loadConversationsFromFiles();
        initializeConversations();

        // Show activation message
        vscode.window.showInformationMessage('🤖 Augura is ready!');

    } catch (error) {
        console.error('❌ Error during activation:', error);
        vscode.window.showErrorMessage(`Failed to activate AI Agent: ${error.message}`);
    }
}

/**
 * Register webview providers for sidebar
 */
function registerWebviewProviders(context) {
    // Chat webview provider
    chatWebviewProvider = new ChatWebviewProvider(context.extensionUri);
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider('augura-chat', chatWebviewProvider)
    );

    // Make chatWebviewProvider globally accessible for AI Agent
    global.chatWebviewProvider = chatWebviewProvider;

    // Files tree provider
    filesTreeProvider = new FilesTreeProvider();
    context.subscriptions.push(
        vscode.window.registerTreeDataProvider('augura-files', filesTreeProvider)
    );

    // Make filesTreeProvider globally accessible for AI Agent
    global.filesTreeProvider = filesTreeProvider;

}

/**
 * Register all extension commands
 */
function registerCommands(context) {

    // Open Chat Command
    const openChatCommand = vscode.commands.registerCommand('augura.openChat', () => {

        openChatSidebar();
    });

    // New Chat Command
    const newChatCommand = vscode.commands.registerCommand('augura.newChat', () => {

        openChatSidebar();
    });



    // Generate Code Command
    const generateCodeCommand = vscode.commands.registerCommand('augura.generateCode', async () => {
        await generateCode();
    });



    // Add all commands to subscriptions
    context.subscriptions.push(
        openChatCommand,
        newChatCommand,
        generateCodeCommand
    );
}

/**
 * Open the chat sidebar for AI Agent interaction
 */
function openChatSidebar() {

    // Focus on the Augura view in sidebar
    vscode.commands.executeCommand('augura-chat.focus');

    // Also reveal the Augura activity bar
    vscode.commands.executeCommand('workbench.view.extension.augura');


}


/**
 * Get webview interface (works for both panel and sidebar)
 */
function getWebviewInterface(webviewView) {
    if (webviewView.webview) {
        // This is a webview view (sidebar)
        return webviewView.webview;
    } else if (webviewView.postMessage) {
        // This is a webview panel
        return webviewView;
    } else {
        // Fallback
        return webviewView;
    }
}

/**
 * Handle messages from the webview
 */
async function handleWebviewMessage(message, webviewView) {
    try {
        switch (message.type) {
            case 'sendMessage':
                await handleUserMessage(message.message, webviewView);
                break;
            case 'clearChat':
                await clearChatHistory(webviewView);
                break;
            case 'requestSavedMessages':
                await loadSavedMessages(webviewView);
                break;
            case 'regenerateMessage':
                await handleRegenerateMessage(message.messageId, webviewView);
                break;
            case 'likeMessage':
                await handleLikeMessage(message.messageId, message.liked, webviewView);
                break;
            case 'dislikeMessage':
                await handleDislikeMessage(message.messageId, message.disliked, webviewView);
                break;
            case 'copyMessage':
                await handleCopyMessage(message.content);
                break;
            case 'editMessage':
                await handleEditMessage(message.messageId, message.newContent, webviewView);
                break;
            case 'removeMessage':
                await handleRemoveMessage(message.messageId, webviewView);
                break;
            case 'openFile':
                await handleOpenFile(message.fileName);
                break;
            case 'removeFileFromList':
                await handleRemoveFileFromList(message.fileName, webviewView);
                break;

            case 'requestThreadsList':
                await handleRequestThreadsList(webviewView);
                break;
            case 'switchThread':
                await handleSwitchThread(message.threadId, webviewView);
                break;
            case 'deleteThread':
                await handleDeleteThread(message.threadId, webviewView);
                break;
            case 'createNewThread':
                await handleCreateNewThread(webviewView);
                break;
            case 'showMessage':
                handleShowMessage(message.message, message.level);
                break;
            default:
        }
    } catch (error) {
        console.error('Error handling webview message:', error);
        panel.webview.postMessage({
            type: 'error',
            message: 'An error occurred: ' + error.message
        });
    }
}

/**
 * Handle user messages and generate AI responses
 */
async function handleUserMessage(userMessage, webviewView) {

    // Get the webview interface (works for both panel and sidebar)
    const webview = getWebviewInterface(webviewView);

    // Show typing indicator
    webview.postMessage({
        type: 'typing',
        isTyping: true
    });

    try {
        // Ensure we have a current thread
        if (!currentThreadId) {
            await handleCreateNewThread(webviewView);
        }

        // Update conversation title if this is the first message
        if (conversations.has(currentThreadId)) {
            const conv = conversations.get(currentThreadId);
            if (conv.messages.length === 0) {
                conv.title = userMessage.length > 50 ? userMessage.substring(0, 50) + '...' : userMessage;
                conv.preview = userMessage.length > 100 ? userMessage.substring(0, 100) + '...' : userMessage;
            }
        }

        // Get current workspace context
        const context = await mcpTools.getWorkspaceContext();
        // Process message with AI Agent
        const response = await aiAgent.processMessage(userMessage, context);

        // Send response with streaming
        await streamResponse(response, webviewView);

        // File processing completed

        // Save conversation after response
        await saveCurrentConversation();

        // Mark this as the final message in sequence
        const webview = getWebviewInterface(webviewView);
        webview.postMessage({
            type: 'markFinalMessage',
            messageId: response.messageId
        });

    } catch (error) {
        console.error('❌ Error processing message:', error);
        console.error('❌ Error stack:', error.stack);

        // Determine error type and provide specific guidance
        let errorMessage = '❌ **Error**: ';
        let guidance = '';

        if (error.message.includes('API request failed')) {
            errorMessage += 'Failed to connect to AI service.';
            guidance = '\n\n**Possible solutions:**\n- Check your internet connection\n- Verify your API key in settings\n- Try again in a few moments';
        } else if (error.message.includes('Invalid user message')) {
            errorMessage += 'Invalid message format.';
            guidance = '\n\n**Please:**\n- Make sure your message is not empty\n- Try typing a simple message like "Hello"';
        } else if (error.message.includes('API key')) {
            errorMessage += 'API key configuration issue.';
            guidance = '\n\n**Please:**\n- Check your API key in VS Code settings\n- Go to Settings > Extensions > Augura > API Key';
        } else {
            errorMessage += error.message;
            guidance = '\n\n**Please try:**\n- Refreshing the extension\n- Restarting VS Code\n- Checking the console for more details';
        }

        // Send error message to chat
        webview.postMessage({
            type: 'streamStart',
            role: 'assistant',
            messageId: Date.now().toString()
        });

        webview.postMessage({
            type: 'streamChunk',
            chunk: errorMessage + guidance
        });

        webview.postMessage({
            type: 'streamEnd'
        });

    } finally {
        // Hide typing indicator
        webview.postMessage({
            type: 'typing',
            isTyping: false
        });
    }
}

/**
 * Stream AI response to webview
 */
async function streamResponse(response, webviewView) {


    // Get the webview interface
    const webview = getWebviewInterface(webviewView);

    if (response.stream) {
        // Start streaming
        webview.postMessage({
            type: 'streamStart',
            role: 'assistant',
            messageId: response.messageId
        });

        // Handle streaming response
        try {
            for await (const chunk of response.stream) {

                webview.postMessage({
                    type: 'streamChunk',
                    chunk: chunk
                });
            }
        } catch (error) {
            console.error('❌ Streaming error:', error);
            webview.postMessage({
                type: 'streamChunk',
                chunk: `\n\n❌ **Streaming Error**: ${error.message}`
            });
        }

        // End streaming
        webview.postMessage({
            type: 'streamEnd'
        });

    } else {
        // Handle complete response


        webview.postMessage({
            type: 'streamStart',
            role: 'assistant',
            messageId: response.messageId
        });

        webview.postMessage({
            type: 'streamChunk',
            chunk: response.content
        });

        webview.postMessage({
            type: 'streamEnd'
        });
    }
}





/**
 * Generate code based on user input
 */
async function generateCode() {
    const prompt = await vscode.window.showInputBox({
        prompt: 'Describe the code you want to generate',
        placeHolder: 'e.g., Create a function to sort an array of objects by date'
    });

    if (!prompt) return;

    try {
        vscode.window.showInformationMessage('🚀 Generating code...');

        const editor = vscode.window.activeTextEditor;
        const language = editor ? editor.document.languageId : 'javascript';

        const generatedCode = await aiAgent.generateCode(prompt, language);

        if (editor) {
            // Insert at cursor position
            await editor.edit(editBuilder => {
                editBuilder.insert(editor.selection.active, generatedCode);
            });
        } else {
            // Create new file
            const doc = await vscode.workspace.openTextDocument({
                content: generatedCode,
                language: language
            });
            await vscode.window.showTextDocument(doc);
        }

        vscode.window.showInformationMessage('✅ Code generated successfully!');

    } catch (error) {
        vscode.window.showErrorMessage('Generation failed: ' + error.message);
    }
}



/**
 * Setup auto-analyze functionality
 */
function setupAutoAnalyze(context) {
    const config = vscode.workspace.getConfiguration('augura');
    if (!config.get('autoAnalyze')) return;

    const disposable = vscode.window.onDidChangeActiveTextEditor(async (editor) => {
        if (editor && editor.document.languageId !== 'plaintext') {
            // Auto-analyze after a short delay
            setTimeout(async () => {
                try {
                    const fileContent = editor.document.getText();
                    if (fileContent.length > 50) { // Only analyze non-empty files
                        await aiAgent.quickAnalyze(fileContent, editor.document.languageId);
                    }
                } catch (error) {
                    console.error('Auto-analyze error:', error);
                }
            }, 2000);
        }
    });

    context.subscriptions.push(disposable);
}

/**
 * Clear chat history
 */
async function clearChatHistory(webviewView) {
    const webview = getWebviewInterface(webviewView);
    webview.postMessage({
        type: 'clearMessages'
    });
}

/**
 * Load saved messages
 */
async function loadSavedMessages(webviewView) {


    try {
        const webview = getWebviewInterface(webviewView);

        // Ensure we have a current thread
        if (!currentThreadId) {
            initializeConversations();
        }

        // Load messages from current conversation
        if (conversations.has(currentThreadId)) {
            const conv = conversations.get(currentThreadId);
            const messages = conv.messages.map(msg => ({
                role: msg.role,
                content: msg.content,
                id: msg.messageId || msg.id || Date.now().toString(),
                liked: msg.liked || false,
                disliked: msg.disliked || false
            }));

            webview.postMessage({
                type: 'savedMessages',
                messages: messages
            });


        } else {
            // Send empty array for new conversation
            webview.postMessage({
                type: 'savedMessages',
                messages: []
            });
        }

    } catch (error) {
        console.error('❌ Error loading saved messages:', error);
        const webview = getWebviewInterface(webviewView);
        webview.postMessage({
            type: 'savedMessages',
            messages: []
        });
    }
}

/**
 * Handle message regeneration
 */
async function handleRegenerateMessage(messageId, webviewView) {


    try {
        // Get the last user message from conversation history
        const userMessages = aiAgent.conversationHistory.filter(msg => msg.role === 'user');
        if (userMessages.length === 0) {
            throw new Error('No user message found to regenerate');
        }

        const lastUserMessage = userMessages[userMessages.length - 1];

        // Remove the last AI response from history
        const lastIndex = aiAgent.conversationHistory.length - 1;
        if (lastIndex >= 0 && aiAgent.conversationHistory[lastIndex].role === 'assistant') {
            aiAgent.conversationHistory.pop();
        }

        // Process the message again
        const context = await mcpTools.getWorkspaceContext();
        const response = await aiAgent.processMessage(lastUserMessage.content, context);

        // Stream the new response
        await streamResponse(response, webviewView);

        vscode.window.showInformationMessage('✅ Message regenerated successfully');

    } catch (error) {
        console.error('❌ Error regenerating message:', error);
        vscode.window.showErrorMessage(`Failed to regenerate message: ${error.message}`);
    }
}

/**
 * Handle message like
 */
async function handleLikeMessage(messageId, liked, webviewView) {


    const webview = getWebviewInterface(webviewView);

    // Update the UI
    webview.postMessage({
        type: 'updateMessageReaction',
        messageId: messageId,
        liked: liked,
        disliked: false // Clear dislike when liking
    });

    // Here you could save the reaction to a database or file

}

/**
 * Handle message dislike
 */
async function handleDislikeMessage(messageId, disliked, webviewView) {


    const webview = getWebviewInterface(webviewView);

    // Update the UI
    webview.postMessage({
        type: 'updateMessageReaction',
        messageId: messageId,
        liked: false, // Clear like when disliking
        disliked: disliked
    });

    // Here you could save the reaction to a database or file

}

/**
 * Handle message copy
 */
async function handleCopyMessage(content) {


    try {
        await vscode.env.clipboard.writeText(content);
        vscode.window.showInformationMessage('📋 Message copied to clipboard');
    } catch (error) {
        console.error('❌ Error copying message:', error);
        vscode.window.showErrorMessage('Failed to copy message to clipboard');
    }
}

/**
 * Handle message edit
 */
async function handleEditMessage(messageId, newContent, webviewView) {


    try {
        // Find and update the message in conversation history
        const messageIndex = aiAgent.conversationHistory.findIndex(
            msg => msg.messageId === messageId || msg.id === messageId
        );

        if (messageIndex !== -1) {
            aiAgent.conversationHistory[messageIndex].content = newContent;

        }

        // Send confirmation back to webview
        const webview = getWebviewInterface(webviewView);
        webview.postMessage({
            type: 'editMessageComplete',
            messageId: messageId,
            newContent: newContent
        });

        vscode.window.showInformationMessage('✅ Message edited successfully');

    } catch (error) {
        console.error('❌ Error editing message:', error);
        vscode.window.showErrorMessage(`Failed to edit message: ${error.message}`);
    }
}

/**
 * Handle message removal
 */
async function handleRemoveMessage(messageId, webviewView) {


    try {
        // Remove from conversation history
        aiAgent.conversationHistory = aiAgent.conversationHistory.filter(
            msg => msg.messageId !== messageId && msg.id !== messageId
        );

        // Send confirmation back to webview
        const webview = getWebviewInterface(webviewView);
        webview.postMessage({
            type: 'removeMessage',
            messageId: messageId
        });

        vscode.window.showInformationMessage('✅ Message removed successfully');

    } catch (error) {
        console.error('❌ Error removing message:', error);
        vscode.window.showErrorMessage(`Failed to remove message: ${error.message}`);
    }
}

/**
 * Handle show message
 */
function handleShowMessage(message, level = 'info') {
    switch (level) {
        case 'error':
            vscode.window.showErrorMessage(message);
            break;
        case 'warning':
            vscode.window.showWarningMessage(message);
            break;
        case 'info':
        default:
            vscode.window.showInformationMessage(message);
            break;
    }
}

/**
 * Handle file opening
 */
async function handleOpenFile(fileName) {


    try {
        // Get workspace folder
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            vscode.window.showErrorMessage('No workspace folder found');
            return;
        }

        // Create full path
        const fullPath = path.join(workspaceFolder.uri.fsPath, fileName);
        const uri = vscode.Uri.file(fullPath);

        // Check if file exists
        if (fs.existsSync(fullPath)) {
            await vscode.window.showTextDocument(uri);

        } else {
            vscode.window.showErrorMessage(`File not found: ${fileName}`);
        }
    } catch (error) {
        console.error('❌ Error opening file:', error);
        vscode.window.showErrorMessage(`Failed to open file: ${error.message}`);
    }
}

/**
 * Handle file removal from list
 */
async function handleRemoveFileFromList(fileName) {


    try {
        // Update files tree
        if (global.filesTreeProvider) {
            // Remove from project files
            const updatedFiles = global.filesTreeProvider.projectFiles.filter(
                file => file.label !== fileName
            );
            global.filesTreeProvider.projectFiles = updatedFiles;
            global.filesTreeProvider.refresh();
        }

        vscode.window.showInformationMessage(`✅ Removed ${fileName} from list`);

    } catch (error) {
        console.error('❌ Error removing file from list:', error);
        vscode.window.showErrorMessage(`Failed to remove file from list: ${error.message}`);
    }
}



/**
 * Handle request for threads list
 */
async function handleRequestThreadsList(webviewView) {


    try {
        const threads = Array.from(conversations.values()).map(conv => ({
            id: conv.id,
            title: conv.title,
            preview: conv.preview,
            lastActivity: conv.lastActivity,
            isActive: conv.id === currentThreadId
        }));

        // Sort by last activity (newest first)
        threads.sort((a, b) => new Date(b.lastActivity) - new Date(a.lastActivity));

        const webview = getWebviewInterface(webviewView);
        webview.postMessage({
            type: 'threadsUpdated',
            threads: threads
        });



    } catch (error) {
        console.error('❌ Error getting threads list:', error);
    }
}

/**
 * Handle thread switch
 */
async function handleSwitchThread(threadId, webviewView) {


    try {
        // Save current conversation if exists
        if (currentThreadId && conversations.has(currentThreadId)) {
            const currentConv = conversations.get(currentThreadId);
            currentConv.messages = [...aiAgent.conversationHistory];
            currentConv.lastActivity = new Date().toISOString();
            conversations.set(currentThreadId, currentConv);
        }

        // Load new conversation
        if (conversations.has(threadId)) {
            currentThreadId = threadId;

            // Load conversation messages if not already loaded
            await loadConversationMessages(threadId);

            const targetConv = conversations.get(threadId);

            // Restore conversation history
            aiAgent.conversationHistory = [...(targetConv.messages || [])];
            lastSavedMessageCount = aiAgent.conversationHistory.length; // Update saved count for switched conversation

            // Clear current chat and load messages
            const webview = getWebviewInterface(webviewView);
            webview.postMessage({
                type: 'clearMessages'
            });

            // Send saved messages
            webview.postMessage({
                type: 'savedMessages',
                messages: targetConv.messages.map(msg => ({
                    role: msg.role,
                    content: msg.content,
                    id: msg.messageId || msg.id,
                    liked: msg.liked || false,
                    disliked: msg.disliked || false
                }))
            });

            // Update thread switch status
            webview.postMessage({
                type: 'threadSwitched',
                threadId: threadId
            });

            vscode.window.showInformationMessage(`✅ Switched to: ${targetConv.title}`);
        }

    } catch (error) {
        console.error('❌ Error switching thread:', error);
        vscode.window.showErrorMessage(`Failed to switch thread: ${error.message}`);
    }
}

/**
 * Handle thread deletion
 */
async function handleDeleteThread(threadId, webviewView) {


    try {
        if (conversations.has(threadId)) {
            const conv = conversations.get(threadId);

            const result = await vscode.window.showWarningMessage(
                `Are you sure you want to delete "${conv.title}"?`,
                { modal: true },
                'Delete',
                'Cancel'
            );

            if (result === 'Delete') {
                conversations.delete(threadId);

                // Delete conversation file
                await deleteConversationFile(threadId);

                // If this was the current thread, create a new one
                if (currentThreadId === threadId) {
                    await handleCreateNewThread(webviewView);
                }

                // Update threads list
                await handleRequestThreadsList(webviewView);

                // Save index after deletion
                await saveConversationsIndex();

                vscode.window.showInformationMessage(`✅ Deleted: ${conv.title}`);
            }
        }

    } catch (error) {
        console.error('❌ Error deleting thread:', error);
        vscode.window.showErrorMessage(`Failed to delete thread: ${error.message}`);
    }
}

/**
 * Handle new thread creation
 */
async function handleCreateNewThread(webviewView) {


    try {
        // Save current conversation if exists
        if (currentThreadId && conversations.has(currentThreadId)) {
            const currentConv = conversations.get(currentThreadId);
            currentConv.messages = [...aiAgent.conversationHistory];
            currentConv.lastActivity = new Date().toISOString();
            conversations.set(currentThreadId, currentConv);
        }

        // Create new thread
        conversationCounter++;
        const newThreadId = `thread-${Date.now()}-${conversationCounter}`;
        const newConversation = {
            id: newThreadId,
            title: 'New Chat',
            preview: 'Start a new conversation...',
            messages: [],
            lastActivity: new Date().toISOString(),
            created: new Date().toISOString()
        };

        conversations.set(newThreadId, newConversation);
        currentThreadId = newThreadId;
        lastSavedMessageCount = 0; // Reset saved message count for new conversation

        // Clear current conversation
        aiAgent.conversationHistory = [];
        aiAgent.currentProject = null;
        aiAgent.projectFiles = [];
        aiAgent.currentFileIndex = 0;

        // Clear chat interface
        const webview = getWebviewInterface(webviewView);
        webview.postMessage({
            type: 'clearMessages'
        });

        // Update threads list
        await handleRequestThreadsList(webviewView);

        // Update files tree
        if (global.filesTreeProvider) {
            global.filesTreeProvider.updateProjectFiles([]);
        }

        // Save new conversation and index
        await saveConversationToFile(newThreadId);
        await saveConversationsIndex();

        vscode.window.showInformationMessage('✅ New conversation started');

    } catch (error) {
        console.error('❌ Error creating new thread:', error);
        vscode.window.showErrorMessage(`Failed to create new thread: ${error.message}`);
    }
}

/**
 * Save current conversation with improved handling
 */
async function saveCurrentConversation() {
    if (!currentThreadId || !conversations.has(currentThreadId)) {
        return;
    }

    try {
        const currentMessageCount = aiAgent.conversationHistory.length;

        // Skip saving if no new messages since last save
        if (currentMessageCount === lastSavedMessageCount) {

            return;
        }

        const conv = conversations.get(currentThreadId);

        // Deep copy the conversation history to avoid reference issues
        conv.messages = aiAgent.conversationHistory.map(msg => ({
            role: msg.role,
            content: msg.content,
            messageId: msg.messageId,
            timestamp: msg.timestamp || new Date().toISOString(),
            liked: msg.liked || false,
            disliked: msg.disliked || false
        }));

        conv.lastActivity = new Date().toISOString();
        conv.messageCount = conv.messages.length;

        // Update preview with last message
        if (conv.messages.length > 0) {
            const lastMessage = conv.messages[conv.messages.length - 1];
            conv.preview = lastMessage.content.length > 100 ?
                lastMessage.content.substring(0, 100) + '...' :
                lastMessage.content;
        }

        conversations.set(currentThreadId, conv);
        lastSavedMessageCount = currentMessageCount; // Update saved count


        // Debounced save to file to improve performance
        debouncedSaveToFile();

    } catch (error) {
        console.error('❌ Error saving conversation:', error);
    }
}

// Debounced save function to improve performance
let saveTimeout = null;
function debouncedSaveToFile() {
    if (saveTimeout) {
        clearTimeout(saveTimeout);
    }
    saveTimeout = setTimeout(async () => {
        if (currentThreadId) {
            await saveConversationToFile(currentThreadId);
        }
        saveTimeout = null;
    }, 1000); // Save after 1 second of inactivity
}

/**
 * Initialize conversations system
 */
function initializeConversations() {
    // Create initial conversation if none exists
    if (conversations.size === 0) {
        conversationCounter++;
        const initialThreadId = `thread-${Date.now()}-${conversationCounter}`;
        const initialConversation = {
            id: initialThreadId,
            title: 'New Chat',
            preview: 'Start a new conversation...',
            messages: [],
            lastActivity: new Date().toISOString(),
            created: new Date().toISOString()
        };

        conversations.set(initialThreadId, initialConversation);
        currentThreadId = initialThreadId;


    }
}

/**
 * Save conversation to individual file
 */
async function saveConversationToFile(threadId) {
    try {
        if (!conversations.has(threadId)) {
            console.warn('⚠️ Conversation not found:', threadId);
            return;
        }

        // Ensure conversations directory exists
        if (!fs.existsSync(conversationsDir)) {
            fs.mkdirSync(conversationsDir, { recursive: true });
        }

        const conversation = conversations.get(threadId);
        const conversationFilePath = path.join(conversationsDir, `${threadId}.json`);

        // Save individual conversation
        fs.writeFileSync(conversationFilePath, JSON.stringify(conversation, null, 2));


        // Update index
        await saveConversationsIndex();

    } catch (error) {
        console.error('❌ Error saving conversation to file:', error);
    }
}

/**
 * Save conversations index
 */
async function saveConversationsIndex() {
    try {
        // Ensure directory exists
        const dir = path.dirname(conversationsIndexPath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }

        // Create index with conversation metadata only
        const indexData = {
            conversations: {},
            currentThreadId: currentThreadId,
            conversationCounter: conversationCounter,
            lastSaved: new Date().toISOString()
        };

        // Add conversation metadata to index
        for (const [threadId, conv] of conversations) {
            indexData.conversations[threadId] = {
                id: conv.id,
                title: conv.title,
                preview: conv.preview,
                lastActivity: conv.lastActivity,
                created: conv.created,
                messageCount: conv.messageCount || conv.messages?.length || 0
            };
        }

        fs.writeFileSync(conversationsIndexPath, JSON.stringify(indexData, null, 2));


    } catch (error) {
        console.error('❌ Error saving conversations index:', error);
    }
}

/**
 * Load conversations from individual files
 */
async function loadConversationsFromFiles() {
    try {
        // Check for migration from old system
        const migrated = await migrateFromOldSystem();
        if (migrated) {

        }

        // Load index first
        if (fs.existsSync(conversationsIndexPath)) {
            const indexData = fs.readFileSync(conversationsIndexPath, 'utf8');
            const parsedIndex = JSON.parse(indexData);

            currentThreadId = parsedIndex.currentThreadId || null;
            conversationCounter = parsedIndex.conversationCounter || 0;



            // Load conversation metadata from index
            const conversationMetadata = parsedIndex.conversations || {};

            // Initialize conversations map with metadata only
            conversations = new Map();
            for (const [threadId, metadata] of Object.entries(conversationMetadata)) {
                conversations.set(threadId, {
                    ...metadata,
                    messages: [] // Messages will be loaded on demand
                });
            }



            // Load current conversation messages if exists
            if (currentThreadId && conversations.has(currentThreadId)) {
                await loadConversationMessages(currentThreadId);
                const currentConv = conversations.get(currentThreadId);
                aiAgent.conversationHistory = [...(currentConv.messages || [])];
                lastSavedMessageCount = aiAgent.conversationHistory.length;

            }
        } else {

        }

    } catch (error) {
        console.error('❌ Error loading conversations from files:', error);
        // Reset to empty state on error
        conversations = new Map();
        currentThreadId = null;
        conversationCounter = 0;
    }
}

/**
 * Load messages for a specific conversation
 */
async function loadConversationMessages(threadId) {
    try {
        const conversationFilePath = path.join(conversationsDir, `${threadId}.json`);

        if (fs.existsSync(conversationFilePath)) {
            const data = fs.readFileSync(conversationFilePath, 'utf8');
            const conversationData = JSON.parse(data);

            // Update conversation with full data
            if (conversations.has(threadId)) {
                const existingConv = conversations.get(threadId);
                conversations.set(threadId, {
                    ...existingConv,
                    ...conversationData
                });

            }
        } else {
            console.warn(`⚠️ Conversation file not found: ${threadId}.json`);
        }

    } catch (error) {
        console.error(`❌ Error loading conversation messages for ${threadId}:`, error);
    }
}

/**
 * Delete conversation file
 */
async function deleteConversationFile(threadId) {
    try {
        const conversationFilePath = path.join(conversationsDir, `${threadId}.json`);

        if (fs.existsSync(conversationFilePath)) {
            fs.unlinkSync(conversationFilePath);

        }

    } catch (error) {
        console.error(`❌ Error deleting conversation file for ${threadId}:`, error);
    }
}

/**
 * Migrate from old single file system to new individual files system
 */
async function migrateFromOldSystem() {
    try {
        const oldFilePath = path.join(path.dirname(conversationsIndexPath), 'conversations.json');

        if (fs.existsSync(oldFilePath)) {


            const oldData = fs.readFileSync(oldFilePath, 'utf8');
            const parsedOldData = JSON.parse(oldData);

            // Ensure new directories exist
            if (!fs.existsSync(conversationsDir)) {
                fs.mkdirSync(conversationsDir, { recursive: true });
            }

            // Migrate conversations
            const oldConversations = parsedOldData.conversations || {};
            for (const [threadId, conversation] of Object.entries(oldConversations)) {
                const conversationFilePath = path.join(conversationsDir, `${threadId}.json`);
                fs.writeFileSync(conversationFilePath, JSON.stringify(conversation, null, 2));

            }

            // Create new index
            currentThreadId = parsedOldData.currentThreadId || null;
            conversationCounter = parsedOldData.conversationCounter || 0;

            // Load conversations into memory
            conversations = new Map(Object.entries(oldConversations));

            // Save new index
            await saveConversationsIndex();

            // Backup old file and delete
            const backupPath = oldFilePath + '.backup';
            fs.renameSync(oldFilePath, backupPath);



            return true;
        }

        return false;

    } catch (error) {
        console.error('❌ Error during migration:', error);
        return false;
    }
}

/**
 * Get error HTML for webview
 */
function getErrorHTML(errorMessage) {
    return `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Error</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; color: #ff6b6b; }
                .error { background: #ffe6e6; padding: 15px; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class="error">
                <h3>Error Loading Chat Interface</h3>
                <p>${errorMessage}</p>
            </div>
        </body>
        </html>
    `;
}





/**
 * Extension deactivation function
 */
function deactivate() {

    if (aiAgent) {
        aiAgent.cleanup();
    }
    if (mcpTools) {
        mcpTools.cleanup();
    }
}

/**
 * Chat Webview Provider for sidebar
 */
class ChatWebviewProvider {
    constructor(extensionUri) {
        this.extensionUri = extensionUri;
        this._view = undefined;
    }

    resolveWebviewView(webviewView, _context, _token) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                vscode.Uri.joinPath(this.extensionUri, 'templates')
            ],
            retainContextWhenHidden: true
        };

        // Load the chat interface
        this.loadChatInterface(webviewView);

        // Handle messages from webview
        webviewView.webview.onDidReceiveMessage(
            async (message) => {
                await handleWebviewMessage(message, webviewView);
            }
        );
    }

    loadChatInterface(webviewView) {
        const templatePath = path.join(this.extensionUri.fsPath, 'templates', 'chat.html');

        try {
            if (!fs.existsSync(templatePath)) {
                throw new Error(`Template file not found: ${templatePath}`);
            }

            let html = fs.readFileSync(templatePath, 'utf8');

            // Convert relative paths to webview URIs
            const scriptPath = path.join(this.extensionUri.fsPath, 'templates', 'script', 'script.js');
            const stylePath = path.join(this.extensionUri.fsPath, 'templates', 'styles', 'style.css');

            const scriptUri = webviewView.webview.asWebviewUri(vscode.Uri.file(scriptPath));
            const styleUri = webviewView.webview.asWebviewUri(vscode.Uri.file(stylePath));

            // Replace relative paths with webview URIs
            html = html.replace('script/script.js', scriptUri.toString());
            html = html.replace('styles/style.css', styleUri.toString());

            // Keep the original Augura branding
            // No need to replace the welcome message as it's already correct

            webviewView.webview.html = html;


            // Send saved messages after a short delay to ensure webview is ready
            setTimeout(() => {
                this.sendSavedMessages(webviewView);
            }, 500);
        } catch (error) {
            console.error('❌ Error loading chat interface:', error);
            webviewView.webview.html = getErrorHTML(error.message);
        }
    }

    sendSavedMessages(webviewView) {
        try {
            if (currentThreadId && conversations.has(currentThreadId)) {
                const conversation = conversations.get(currentThreadId);
                if (conversation && conversation.messages && conversation.messages.length > 0) {
                    webviewView.webview.postMessage({
                        type: 'restoreMessages',
                        messages: conversation.messages
                    });

                }
            }
        } catch (error) {
            console.error('❌ Error sending saved messages:', error);
        }
    }

    postMessage(message) {
        if (this._view) {
            this._view.webview.postMessage(message);
        }
    }
}

/**
 * Files Tree Provider for project files
 */
class FilesTreeProvider {
    constructor() {
        this._onDidChangeTreeData = new vscode.EventEmitter();
        this.onDidChangeTreeData = this._onDidChangeTreeData.event;
        this.projectFiles = [];
    }

    refresh() {
        this._onDidChangeTreeData.fire();
    }

    getTreeItem(element) {
        return element;
    }

    getChildren(element) {
        if (!element) {
            // Root level - show project files
            return this.projectFiles;
        }
        return [];
    }

    updateProjectFiles(files) {
        this.projectFiles = files.map(file => {
            const item = new vscode.TreeItem(file.name, vscode.TreeItemCollapsibleState.None);
            item.description = file.description;
            item.tooltip = `${file.name} - ${file.description}`;
            item.iconPath = new vscode.ThemeIcon('file');
            item.command = {
                command: 'vscode.open',
                title: 'Open File',
                arguments: [vscode.Uri.file(file.path)]
            };
            return item;
        });
        this.refresh();
    }
}

module.exports = {
    activate,
    deactivate
};
