<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loading Augura Coder</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@vscode/codicons@0.0.35/dist/codicon.css">
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 24px;
            max-width: 300px;
            width: 100%;
            padding: 32px;
            text-align: center;
        }

        .loading-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .loading-logo-image {
            width: 80px;
            height: 80px;
            border-radius: 12px;
            animation: pulse 2s ease-in-out infinite;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            border: 2px solid var(--vscode-textLink-foreground);
        }

        .loading-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--vscode-foreground);
            margin: 0;
        }

        .loading-subtitle {
            font-size: 14px;
            color: var(--vscode-descriptionForeground);
            margin: 8px 0 0 0;
            opacity: 0.8;
        }

        .progress-container {
            width: 100%;
            margin-top: 32px;
        }

        .progress-bar-container {
            width: 100%;
            height: 6px;
            background-color: var(--vscode-input-background);
            border-radius: 3px;
            overflow: hidden;
            border: 1px solid var(--vscode-input-border);
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg,
                var(--vscode-textLink-foreground) 0%,
                var(--vscode-button-background) 100%);
            width: 0%;
            transition: width 0.5s ease-out;
            border-radius: 2px;
            position: relative;
            overflow: hidden;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.3) 50%,
                transparent 100%);
            animation: shimmer 2s infinite;
        }

        .progress-text {
            margin-top: 12px;
            font-size: 13px;
            color: var(--vscode-descriptionForeground);
            min-height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .progress-percentage {
            font-weight: 500;
            color: var(--vscode-foreground);
        }

        .loading-dots {
            display: inline-block;
        }

        .loading-dots::after {
            content: '...';
            animation: dots 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.05);
                opacity: 0.8;
            }
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }

        .version-info {
            position: absolute;
            bottom: 16px;
            right: 16px;
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            opacity: 0.6;
        }

        /* Codicon styles */
        .codicon {
            font-family: 'codicon';
            font-style: normal;
            font-weight: normal;
            font-variant: normal;
            text-decoration: inherit;
            text-transform: none;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        @font-face {
            font-family: 'codicon';
            src: url('https://cdn.jsdelivr.net/npm/@vscode/codicons@0.0.35/dist/codicon.ttf') format('truetype');
        }
    </style>
</head>
<body>
    <div class="loading-container">
        <div class="loading-logo">
            <img src="${logoUri}" alt="Augura Coder Logo" class="loading-logo-image">
        </div>

        <h1 class="loading-title">Augura Coder</h1>
        <p class="loading-subtitle">AI Programming Assistant</p>

        <div class="progress-container">
            <div class="progress-bar-container">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            <div class="progress-text">
                <span class="progress-percentage" id="progressPercentage">0%</span>
                <span id="progressMessage">Initializing<span class="loading-dots"></span></span>
            </div>
        </div>
    </div>

    <div class="version-info">
        v1.0.0 -Alpha
    </div>

    <script>
        const vscode = acquireVsCodeApi();

        // Handle messages from extension
        window.addEventListener('message', event => {
            const message = event.data;

            if (message.type === 'updateProgress') {
                updateProgress(message.progress, message.message);
            }
        });

        function updateProgress(percentage, message) {
            const progressBar = document.getElementById('progressBar');
            const progressPercentage = document.getElementById('progressPercentage');
            const progressMessage = document.getElementById('progressMessage');

            progressBar.style.width = percentage + '%';
            progressPercentage.textContent = percentage + '%';
            progressMessage.innerHTML = message + (percentage < 100 ? '<span class="loading-dots"></span>' : '');

            // If loading is complete, notify extension
            if (percentage >= 100) {
                setTimeout(() => {
                    vscode.postMessage({
                        type: 'loadingComplete'
                    });
                }, 1000);
            }
        }
    </script>
</body>
</html>
