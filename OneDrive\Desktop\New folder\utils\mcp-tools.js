const vscode = require('vscode');
const fs = require('fs').promises;
const path = require('path');

/**
 * MCP (Model Context Protocol) Tools for VS Code integration
 */
class MCPTools {
    constructor() {
        this.workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        this.tools = this.initializeTools();

    }

    /**
     * Initialize available tools
     */
    initializeTools() {
        return {
            // File operations
            readFile: this.readFile.bind(this),
            writeFile: this.writeFile.bind(this),
            createFile: this.createFile.bind(this),
            // Directory operations
            listDirectory: this.listDirectory.bind(this),

            // VS Code operations
            openFile: this.openFile.bind(this),

            // Workspace operations
            getWorkspaceInfo: this.getWorkspaceInfo.bind(this),
            findFiles: this.findFiles.bind(this),
            getProjectStructure: this.getProjectStructure.bind(this),
            getAllProjectFiles: this.getAllProjectFiles.bind(this),
            getFileSummary: this.getFileSummary.bind(this)
        };
    }

    /**
     * Get workspace context for AI
     */
    async getWorkspaceContext() {
        try {
            const context = {
                workspaceRoot: this.workspaceRoot,
                openFiles: await this.getOpenFiles(),
                activeFile: await this.getActiveFile(),
                workspaceInfo: await this.getWorkspaceInfo(),
                projectStructure: await this.getProjectStructure(),
                allFiles: await this.getAllProjectFiles()
            };

            return context;
        } catch (error) {
            console.error('Error getting workspace context:', error);
            return null;
        }
    }

    /**
     * Read file content
     */
    async readFile(filePath) {
        try {
            const fullPath = this.resolvePath(filePath);
            const content = await fs.readFile(fullPath, 'utf8');
            return {
                success: true,
                content: content,
                path: fullPath
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Write content to file
     */
    async writeFile(filePath, content) {
        try {
            const fullPath = this.resolvePath(filePath);
            await fs.writeFile(fullPath, content, 'utf8');

            // Refresh VS Code if file is open
            await this.refreshOpenFile(fullPath);

            return {
                success: true,
                path: fullPath
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Create new file
     */
    async createFile(filePath, content = '') {
        try {
            const fullPath = this.resolvePath(filePath);

            // Create directory if it doesn't exist
            const dir = path.dirname(fullPath);
            await fs.mkdir(dir, { recursive: true });

            await fs.writeFile(fullPath, content, 'utf8');

            return {
                success: true,
                path: fullPath
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }


    /**
     * List directory contents
     */
    async listDirectory(dirPath = '.') {
        try {
            const fullPath = this.resolvePath(dirPath);
            const items = await fs.readdir(fullPath, { withFileTypes: true });

            const contents = items.map(item => ({
                name: item.name,
                path: path.join(fullPath, item.name),
                type: item.isDirectory() ? 'directory' : 'file'
            }));

            return {
                success: true,
                contents: contents
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Open file in VS Code
     */
    async openFile(filePath) {
        try {
            const fullPath = this.resolvePath(filePath);
            const uri = vscode.Uri.file(fullPath);
            const document = await vscode.workspace.openTextDocument(uri);
            await vscode.window.showTextDocument(document);

            return {
                success: true,
                path: fullPath
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }


    /**
     * Get workspace information
     */
    async getWorkspaceInfo() {
        try {
            const workspaceFolders = vscode.workspace.workspaceFolders;
            const info = {
                name: vscode.workspace.name,
                folders: workspaceFolders?.map(folder => ({
                    name: folder.name,
                    path: folder.uri.fsPath
                })) || [],
                settings: vscode.workspace.getConfiguration().get('ai-agent')
            };

            return {
                success: true,
                info: info
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Find files by pattern
     */
    async findFiles(pattern, exclude = null) {
        try {
            const files = await vscode.workspace.findFiles(pattern, exclude);
            const filePaths = files.map(file => file.fsPath);

            return {
                success: true,
                files: filePaths
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }


    // Helper methods

    /**
     * Resolve relative path to absolute
     */
    resolvePath(filePath) {
        if (path.isAbsolute(filePath)) {
            return filePath;
        }

        if (!this.workspaceRoot) {
            throw new Error('No workspace root available');
        }

        return path.join(this.workspaceRoot, filePath);
    }

    /**
     * Get currently open files
     */
    async getOpenFiles() {
        const openFiles = vscode.workspace.textDocuments
            .filter(doc => !doc.isUntitled)
            .map(doc => ({
                path: doc.fileName,
                language: doc.languageId,
                isDirty: doc.isDirty
            }));

        return openFiles;
    }

    /**
     * Get active file info
     */
    async getActiveFile() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) return null;

        return {
            path: editor.document.fileName,
            language: editor.document.languageId,
            selection: {
                start: editor.selection.start,
                end: editor.selection.end,
                text: editor.document.getText(editor.selection)
            }
        };
    }


    /**
     * Refresh open file in VS Code
     */
    async refreshOpenFile(filePath) {
        try {
            const openDoc = vscode.workspace.textDocuments.find(
                doc => doc.fileName === filePath
            );

            if (openDoc) {
                // File is open, VS Code will handle the refresh
                return true;
            }
        } catch (error) {
            console.error('Error refreshing file:', error);
        }

        return false;
    }


    /**
     * Get complete project structure
     */
    async getProjectStructure() {
        try {
            if (!this.workspaceRoot) {
                return { success: false, error: 'No workspace root available' };
            }

            const structure = await this.buildDirectoryTree(this.workspaceRoot);
            return {
                success: true,
                structure: structure
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get all project files (filtered)
     */
    async getAllProjectFiles() {
        try {
            if (!this.workspaceRoot) {
                return { success: false, error: 'No workspace root available' };
            }

            const files = await this.scanProjectFiles(this.workspaceRoot);
            return {
                success: true,
                files: files
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Build directory tree recursively
     */
    async buildDirectoryTree(dirPath, maxDepth = 3, currentDepth = 0) {
        if (currentDepth >= maxDepth) {
            return { name: path.basename(dirPath), type: 'directory', truncated: true };
        }

        try {
            const items = await fs.readdir(dirPath, { withFileTypes: true });
            const children = [];

            for (const item of items) {
                // Skip ignored directories and files
                if (this.shouldIgnoreItem(item.name)) {
                    continue;
                }

                const itemPath = path.join(dirPath, item.name);

                if (item.isDirectory()) {
                    const subTree = await this.buildDirectoryTree(itemPath, maxDepth, currentDepth + 1);
                    children.push(subTree);
                } else {
                    children.push({
                        name: item.name,
                        type: 'file',
                        path: itemPath,
                        extension: path.extname(item.name)
                    });
                }
            }

            return {
                name: path.basename(dirPath),
                type: 'directory',
                path: dirPath,
                children: children
            };
        } catch (error) {
            return {
                name: path.basename(dirPath),
                type: 'directory',
                error: error.message
            };
        }
    }

    /**
     * Scan all project files recursively
     */
    async scanProjectFiles(dirPath, files = [], maxFiles = 100) {
        if (files.length >= maxFiles) {
            return files;
        }

        try {
            const items = await fs.readdir(dirPath, { withFileTypes: true });

            for (const item of items) {
                if (files.length >= maxFiles) break;

                // Skip ignored items
                if (this.shouldIgnoreItem(item.name)) {
                    continue;
                }

                const itemPath = path.join(dirPath, item.name);

                if (item.isDirectory()) {
                    await this.scanProjectFiles(itemPath, files, maxFiles);
                } else {
                    // Only include relevant file types
                    if (this.isRelevantFile(item.name)) {
                        const stats = await fs.stat(itemPath);
                        files.push({
                            name: item.name,
                            path: itemPath,
                            relativePath: path.relative(this.workspaceRoot, itemPath),
                            extension: path.extname(item.name),
                            size: stats.size,
                            modified: stats.mtime,
                            type: this.getFileType(item.name)
                        });
                    }
                }
            }

            return files;
        } catch (error) {
            console.error('Error scanning files:', error);
            return files;
        }
    }

    /**
     * Check if item should be ignored
     */
    shouldIgnoreItem(itemName) {
        const ignoredItems = [
            'node_modules',
            '.git',
            '.vscode-test',
            'out',
            'dist',
            'build',
            '.DS_Store',
            'Thumbs.db',
            '.env',
            '.env.local',
            '.env.development.local',
            '.env.test.local',
            '.env.production.local',
            'npm-debug.log',
            'yarn-debug.log',
            'yarn-error.log',
            '.nyc_output',
            'coverage',
            '.cache',
            '.parcel-cache',
            '.next',
            '.nuxt',
            '.vuepress/dist',
            '.serverless',
            '.fusebox',
            '.dynamodb',
            '.tern-port'
        ];

        return ignoredItems.includes(itemName) || itemName.startsWith('.');
    }

    /**
     * Check if file is relevant for AI analysis
     */
    isRelevantFile(fileName) {
        const relevantExtensions = [
            '.js', '.jsx', '.ts', '.tsx',
            '.html', '.htm', '.css', '.scss', '.sass', '.less',
            '.json', '.xml', '.yaml', '.yml',
            '.md', '.txt', '.rst',
            '.py', '.java', '.c', '.cpp', '.h', '.hpp',
            '.php', '.rb', '.go', '.rs', '.swift',
            '.vue', '.svelte', '.angular',
            '.sql', '.graphql',
            '.sh', '.bat', '.ps1',
            '.dockerfile', '.gitignore', '.gitattributes',
            '.eslintrc', '.prettierrc', '.babelrc',
            '.package.json', '.tsconfig.json'
        ];

        const extension = path.extname(fileName).toLowerCase();
        const baseName = path.basename(fileName).toLowerCase();

        // Check by extension
        if (relevantExtensions.includes(extension)) {
            return true;
        }

        // Check special files without extensions
        const specialFiles = [
            'readme', 'license', 'changelog', 'contributing',
            'dockerfile', 'makefile', 'rakefile', 'gemfile',
            'package.json', 'composer.json', 'cargo.toml',
            'requirements.txt', 'setup.py', 'pyproject.toml'
        ];

        return specialFiles.includes(baseName);
    }

    /**
     * Get file type category
     */
    getFileType(fileName) {
        const extension = path.extname(fileName).toLowerCase();

        const typeMap = {
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.ts': 'typescript',
            '.tsx': 'typescript',
            '.html': 'html',
            '.htm': 'html',
            '.css': 'stylesheet',
            '.scss': 'stylesheet',
            '.sass': 'stylesheet',
            '.less': 'stylesheet',
            '.json': 'data',
            '.xml': 'data',
            '.yaml': 'data',
            '.yml': 'data',
            '.md': 'documentation',
            '.txt': 'text',
            '.py': 'python',
            '.java': 'java',
            '.php': 'php',
            '.rb': 'ruby',
            '.go': 'go',
            '.rs': 'rust',
            '.swift': 'swift',
            '.vue': 'vue',
            '.svelte': 'svelte'
        };

        return typeMap[extension] || 'other';
    }

    /**
     * Get file summary for AI context
     */
    async getFileSummary(filePath, maxLines = 50) {
        try {
            const content = await fs.readFile(filePath, 'utf8');
            const lines = content.split('\n');

            if (lines.length <= maxLines) {
                return {
                    success: true,
                    content: content,
                    truncated: false,
                    totalLines: lines.length
                };
            }

            // Get first and last portions
            const firstPortion = lines.slice(0, Math.floor(maxLines * 0.7)).join('\n');
            const lastPortion = lines.slice(-Math.floor(maxLines * 0.3)).join('\n');

            return {
                success: true,
                content: firstPortion + '\n\n... [truncated] ...\n\n' + lastPortion,
                truncated: true,
                totalLines: lines.length,
                showingLines: maxLines
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Cleanup resources
     */
    cleanup() {

    }
}

module.exports = { MCPTools };
