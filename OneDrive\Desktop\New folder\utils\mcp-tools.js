const vscode = require('vscode');
const fs = require('fs').promises;
const path = require('path');

/**
 * MCP (Model Context Protocol) Tools for VS Code integration
 */
class MCPTools {
    constructor() {
        this.workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        this.tools = this.initializeTools();

    }

    /**
     * Initialize available tools
     */
    initializeTools() {
        return {
            // File operations
            readFile: this.readFile.bind(this),
            writeFile: this.writeFile.bind(this),
            createFile: this.createFile.bind(this),
            // Directory operations
            listDirectory: this.listDirectory.bind(this),

            // VS Code operations
            openFile: this.openFile.bind(this),

            // Workspace operations
            getWorkspaceInfo: this.getWorkspaceInfo.bind(this),
            findFiles: this.findFiles.bind(this)
        };
    }

    /**
     * Get workspace context for AI
     */
    async getWorkspaceContext() {
        try {
            const context = {
                workspaceRoot: this.workspaceRoot,
                openFiles: await this.getOpenFiles(),
                activeFile: await this.getActiveFile(),
                workspaceInfo: await this.getWorkspaceInfo()
            };

            return context;
        } catch (error) {
            console.error('Error getting workspace context:', error);
            return null;
        }
    }

    /**
     * Read file content
     */
    async readFile(filePath) {
        try {
            const fullPath = this.resolvePath(filePath);
            const content = await fs.readFile(fullPath, 'utf8');
            return {
                success: true,
                content: content,
                path: fullPath
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Write content to file
     */
    async writeFile(filePath, content) {
        try {
            const fullPath = this.resolvePath(filePath);
            await fs.writeFile(fullPath, content, 'utf8');

            // Refresh VS Code if file is open
            await this.refreshOpenFile(fullPath);

            return {
                success: true,
                path: fullPath
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Create new file
     */
    async createFile(filePath, content = '') {
        try {
            const fullPath = this.resolvePath(filePath);

            // Create directory if it doesn't exist
            const dir = path.dirname(fullPath);
            await fs.mkdir(dir, { recursive: true });

            await fs.writeFile(fullPath, content, 'utf8');

            return {
                success: true,
                path: fullPath
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }


    /**
     * List directory contents
     */
    async listDirectory(dirPath = '.') {
        try {
            const fullPath = this.resolvePath(dirPath);
            const items = await fs.readdir(fullPath, { withFileTypes: true });

            const contents = items.map(item => ({
                name: item.name,
                path: path.join(fullPath, item.name),
                type: item.isDirectory() ? 'directory' : 'file'
            }));

            return {
                success: true,
                contents: contents
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Open file in VS Code
     */
    async openFile(filePath) {
        try {
            const fullPath = this.resolvePath(filePath);
            const uri = vscode.Uri.file(fullPath);
            const document = await vscode.workspace.openTextDocument(uri);
            await vscode.window.showTextDocument(document);

            return {
                success: true,
                path: fullPath
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }


    /**
     * Get workspace information
     */
    async getWorkspaceInfo() {
        try {
            const workspaceFolders = vscode.workspace.workspaceFolders;
            const info = {
                name: vscode.workspace.name,
                folders: workspaceFolders?.map(folder => ({
                    name: folder.name,
                    path: folder.uri.fsPath
                })) || [],
                settings: vscode.workspace.getConfiguration().get('ai-agent')
            };

            return {
                success: true,
                info: info
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Find files by pattern
     */
    async findFiles(pattern, exclude = null) {
        try {
            const files = await vscode.workspace.findFiles(pattern, exclude);
            const filePaths = files.map(file => file.fsPath);

            return {
                success: true,
                files: filePaths
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }


    // Helper methods

    /**
     * Resolve relative path to absolute
     */
    resolvePath(filePath) {
        if (path.isAbsolute(filePath)) {
            return filePath;
        }

        if (!this.workspaceRoot) {
            throw new Error('No workspace root available');
        }

        return path.join(this.workspaceRoot, filePath);
    }

    /**
     * Get currently open files
     */
    async getOpenFiles() {
        const openFiles = vscode.workspace.textDocuments
            .filter(doc => !doc.isUntitled)
            .map(doc => ({
                path: doc.fileName,
                language: doc.languageId,
                isDirty: doc.isDirty
            }));

        return openFiles;
    }

    /**
     * Get active file info
     */
    async getActiveFile() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) return null;

        return {
            path: editor.document.fileName,
            language: editor.document.languageId,
            selection: {
                start: editor.selection.start,
                end: editor.selection.end,
                text: editor.document.getText(editor.selection)
            }
        };
    }


    /**
     * Refresh open file in VS Code
     */
    async refreshOpenFile(filePath) {
        try {
            const openDoc = vscode.workspace.textDocuments.find(
                doc => doc.fileName === filePath
            );

            if (openDoc) {
                // File is open, VS Code will handle the refresh
                return true;
            }
        } catch (error) {
            console.error('Error refreshing file:', error);
        }

        return false;
    }


    /**
     * Cleanup resources
     */
    cleanup() {

    }
}

module.exports = { MCPTools };
