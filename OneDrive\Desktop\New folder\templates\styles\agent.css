/* Agent messages */
.agent-message {
    padding: 8px 12px;
    margin: 4px 0;
    border-radius: 6px;
    font-size: 13px;
    border-left: 3px solid;
    background-color: var(--vscode-editor-inactiveSelectionBackground);
    opacity: 0.9;
}

.agent-message.agent-action {
    border-left-color: var(--vscode-charts-blue);
    background-color: rgba(0, 122, 204, 0.1);
}

.agent-message.agent-thinking {
    border-left-color: var(--vscode-charts-yellow);
    background-color: rgba(255, 193, 7, 0.1);
}

.agent-message.agent-task {
    border-left-color: var(--vscode-charts-green);
    background-color: rgba(40, 167, 69, 0.1);
}

.agent-message.agent-file-change {
    border-left-color: var(--vscode-charts-purple);
    background-color: rgba(108, 117, 125, 0.1);
}

.agent-message.agent-success {
    border-left-color: var(--vscode-charts-green);
    background-color: rgba(40, 167, 69, 0.1);
}

.agent-message.agent-error {
    border-left-color: var(--vscode-charts-red);
    background-color: rgba(220, 53, 69, 0.1);
}

.agent-message-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.agent-icon {
    font-size: 14px;
    min-width: 16px;
}

.agent-text {
    flex: 1;
    color: var(--vscode-foreground);
}

.agent-timestamp {
    font-size: 11px;
    color: var(--vscode-descriptionForeground);
    opacity: 0.7;
}

/* Current task display */
.current-task {
    background-color: var(--vscode-editor-background);
    border: 1px solid var(--vscode-panel-border);
    border-radius: 8px;
    margin: 12px 0;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.task-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-weight: 600;
    color: var(--vscode-foreground);
}

.task-header i {
    color: var(--vscode-charts-blue);
}

.task-title {
    font-weight: 600;
    color: var(--vscode-foreground);
    margin-bottom: 6px;
}

.task-description {
    color: var(--vscode-descriptionForeground);
    margin-bottom: 12px;
    line-height: 1.4;
}

.task-progress {
    display: flex;
    align-items: center;
    gap: 12px;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background-color: var(--vscode-progressBar-background);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: var(--vscode-charts-blue);

    border-radius: 3px;
}

.progress-text {
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
    min-width: 35px;
    text-align: right;
}



/* Agent status indicator */
.agent-status {
    position: fixed;
    top: 10px;
    right: 10px;
    background-color: var(--vscode-badge-background);
    color: var(--vscode-badge-foreground);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 4px;
}

.agent-status.active {
    background-color: var(--vscode-charts-green);
}

.agent-status.thinking {
    background-color: var(--vscode-charts-yellow);
}

.agent-status.error {
    background-color: var(--vscode-charts-red);
}
