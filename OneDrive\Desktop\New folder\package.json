{"name": "augura", "displayName": "<PERSON><PERSON>", "description": "Advanced AI coding agent using MCP and Groq API for intelligent coding assistance in VS Code", "version": "1.0.0", "publisher": "augura", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning", "Snippets"], "keywords": ["augura", "ai", "assistant", "coding", "mcp", "groq", "agent"], "main": "./extension.js", "contributes": {"commands": [{"command": "augura.openChat", "title": "Augura: Open Chat", "icon": "$(comment-discussion)"}, {"command": "augura.newChat", "title": "Augura: New Chat", "icon": "$(add)"}, {"command": "augura.generateCode", "title": "Augura: Generate Code", "icon": "$(add)"}], "menus": {"editor/context": [], "explorer/context": []}, "keybindings": [{"command": "augura.openChat", "key": "ctrl+alt+a"}, {"command": "augura.generateCode", "key": "ctrl+alt+g"}], "configuration": {"type": "object", "title": "<PERSON><PERSON>", "properties": {"augura.groqApiKey": {"type": "string", "default": "", "description": "Your Groq API Key (optional - using default if empty)"}, "augura.model": {"type": "string", "default": "moonshotai/kimi-k2-instruct", "description": "AI model to use"}, "augura.autoAnalyze": {"type": "boolean", "default": true, "description": "Automatically analyze code when opening files"}, "augura.streamResponse": {"type": "boolean", "default": true, "description": "Enable streaming responses"}}}, "views": {"augura": [{"id": "augura-chat", "name": "<PERSON><PERSON>", "type": "webview", "when": "true", "icon": "icon.png"}, {"id": "augura-files", "name": "Project Files", "when": "true", "icon": "$(files)"}]}, "viewsContainers": {"activitybar": [{"id": "augura", "title": "<PERSON><PERSON>", "icon": "icon.png"}]}}, "scripts": {"vscode:prepublish": "echo 'No compilation needed for JavaScript extension'", "package": "vsce package", "install-vsce": "npm install -g @vscode/vsce"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "^18.0.0", "@vscode/vsce": "^2.22.0"}, "dependencies": {"axios": "^1.6.0", "uuid": "^9.0.0", "marked": "^9.1.0", "dompurify": "^3.0.6"}, "repository": {"type": "git", "url": "https://github.com/augura/augura.git"}, "license": "MIT", "icon": "icon.png"}