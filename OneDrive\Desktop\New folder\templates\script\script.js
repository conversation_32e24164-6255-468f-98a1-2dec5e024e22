const vscode = acquireVsCodeApi();
const chatContainer = document.getElementById('chatContainer');
const messageInput = document.getElementById('messageInput');
const typingIndicator = document.getElementById('typingIndicator');
const scrollToBottomBtn = document.getElementById('scrollToBottomBtn');

// Threads management
let currentThreads = [];
let threadsPanelOpen = false;

// Configure marked globally
marked.setOptions({
    breaks: true,
    gfm: true,
    tables: true,
    smartLists: true,
    smartypants: true
});

// Configure Prism.js for syntax highlighting with all languages support
window.Prism = window.Prism || {};
window.Prism.manual = true; // Disable automatic highlighting

// Configure autoloader for all languages
document.addEventListener('DOMContentLoaded', () => {
    if (window.Prism && window.Prism.plugins && window.Prism.plugins.autoloader) {
        // Set up autoloader path for all languages
        window.Prism.plugins.autoloader.languages_path = 'https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/';

        // Configure autoloader to load any language automatically
        window.Prism.plugins.autoloader.use_minified = true;



        // Re-highlight any existing code blocks after a short delay
        setTimeout(() => {
            rehighlightAllCode();
        }, 500);
    } else {
        console.warn('Prism.js or autoloader not available');
    }
});

// State management for UI persistence
let uiState = {
    scrollPosition: 0,
    expandedCodeBlocks: new Set(),
    filesChangedExpanded: false
};

// Request saved messages from extension
vscode.postMessage({
    type: 'requestSavedMessages'
});

// Save UI state periodically
setInterval(() => {
    saveUIState();
}, 2000);

// Handle messages from extension
let currentStreamingMessage = null;
let isInThinkingMode = false;
let responseBuffer = '';
let agentMode = true; // Enable agent mode
let currentTask = null;

// Scroll management variables
let userIsScrolling = false;
let autoScrollEnabled = true;
let scrollTimeout = null;

window.addEventListener('message', event => {
    const message = event.data;

    switch (message.type) {
        case 'streamStart':
            startStreamingMessage(message.role, message.messageId);
            break;
        case 'streamChunk':
            addStreamChunk(message.chunk);
            break;
        case 'streamEnd':
            endStreamingMessage();
            break;
        case 'clearMessages':
            clearMessages();
            break;
        case 'typing':
            showTyping(message.isTyping);
            break;
        case 'savedMessages':
        case 'restoreMessages':
            restoreSavedMessages(message.messages);
            break;
        case 'showRetryCountdown':
            showRetryCountdown(message.remainingTime, message.attempt, message.maxAttempts);
            break;
        case 'updateRetryCountdown':
            updateRetryCountdown(message.remainingTime);
            break;
        case 'showRetryThinking':
            showRetryThinking();
            break;
        case 'removeMessage':
            removeMessage(message.messageId);
            break;
        case 'updateMessageReaction':
            updateMessageReaction(message.messageId, message.liked, message.disliked);
            break;
        case 'agentAction':
            handleAgentAction(message.action, message.data);
            break;
        case 'taskUpdate':
            updateCurrentTask(message.task);
            break;

        case 'threadsUpdated':
            updateThreadsList(message.threads);
            break;
        case 'threadSwitched':
            handleThreadSwitch(message.threadId);
            break;
        case 'editMessageComplete':
            handleEditMessageComplete(message.messageId, message.newContent);
            break;
        case 'markFinalMessage':
            markMessageAsFinal(message.messageId);
            break;
        case 'retryNotification':
            showRetryNotification(message.attempt, message.maxAttempts, message.delay, message.errorType);
            break;
        case 'errorNotification':
            showErrorNotification(message.errorType, message.message, message.status, message.retryable);
            break;
    }
});

function startStreamingMessage(role, messageId = null) {
    // Remove welcome message if it exists
    const welcomeMsg = chatContainer.querySelector('.welcome-message');
    if (welcomeMsg) {
        welcomeMsg.remove();
    }

    // Create streaming message container
    currentStreamingMessage = document.createElement('div');
    currentStreamingMessage.className = `message ${role}`;

    // Set message ID if provided
    if (messageId) {
        currentStreamingMessage.setAttribute('data-message-id', messageId);
    }

    const content = document.createElement('div');
    content.className = 'message-content';
    content.innerHTML = '';

    currentStreamingMessage.appendChild(content);

    // Only add to DOM if it's a user message or if we have content
    if (role === 'user') {
        chatContainer.appendChild(currentStreamingMessage);
    }

    // Reset streaming state
    isInThinkingMode = false;
    responseBuffer = '';

    // Show thinking indicator for assistant messages
    if (role === 'assistant') {
        showTyping(true);
    }

    scrollToBottomIfEnabled();
}

function addStreamChunk(chunk) {
    if (!currentStreamingMessage) return;

    const content = currentStreamingMessage.querySelector('.message-content');
    responseBuffer += chunk;

    // Check for thinking tags
    if (responseBuffer.includes('<think>')) {
        isInThinkingMode = true;
        showTyping(true);
    }

    if (isInThinkingMode) {
        // We're in thinking mode, don't display content yet
        if (responseBuffer.includes('</think>')) {
            // End of thinking, extract the response after </think>
            const thinkEndIndex = responseBuffer.indexOf('</think>') + 8;
            const actualResponse = responseBuffer.substring(thinkEndIndex);

            isInThinkingMode = false;
            showTyping(false);

            // Add message to DOM when we have actual content
            if (!currentStreamingMessage.parentNode && actualResponse.trim()) {
                chatContainer.appendChild(currentStreamingMessage);
            }

            // Display the actual response with performance optimization
            if (actualResponse.trim()) {
                // Use requestAnimationFrame for better performance during streaming
                requestAnimationFrame(() => {
                    content.innerHTML = processStreamContent(actualResponse);
                    processCodeBlocksInElement(content);
                    applySyntaxHighlighting(content);
                    applyTextDirection(currentStreamingMessage, actualResponse);
                });
            }
        }
        // Don't display anything while thinking
    } else {
        // Normal streaming mode - add to DOM if not already there
        if (!currentStreamingMessage.parentNode && responseBuffer.trim()) {
            chatContainer.appendChild(currentStreamingMessage);
            showTyping(false);
        }

        if (responseBuffer.trim()) {
            content.innerHTML = processStreamContent(responseBuffer);
            processCodeBlocksInElement(content);
            applySyntaxHighlighting(content);
            applyTextDirection(currentStreamingMessage, responseBuffer);
        }
    }

    scrollToBottomIfEnabled();
}

function endStreamingMessage() {
    if (!currentStreamingMessage) return;

    const content = currentStreamingMessage.querySelector('.message-content');

    // Final processing
    if (!isInThinkingMode && responseBuffer.trim()) {
        // Add to DOM if not already there
        if (!currentStreamingMessage.parentNode) {
            chatContainer.appendChild(currentStreamingMessage);
        }

        // Remove any remaining <think> content
        let finalContent = responseBuffer;
        if (finalContent.includes('<think>') && finalContent.includes('</think>')) {
            const thinkStart = finalContent.indexOf('<think>');
            const thinkEnd = finalContent.indexOf('</think>') + 8;
            finalContent = finalContent.substring(0, thinkStart) + finalContent.substring(thinkEnd);
        }

        content.innerHTML = processStreamContent(finalContent);
        processCodeBlocksInElement(content);
        applySyntaxHighlighting(content);
        applyTextDirection(currentStreamingMessage, finalContent);

        // Don't add action buttons automatically - they will be added only for final messages
        // Mark this message as potentially needing actions later
        if (currentStreamingMessage.classList.contains('assistant')) {
            currentStreamingMessage.setAttribute('data-needs-actions', 'true');
        }

        // Add action buttons for user messages
        if (currentStreamingMessage.classList.contains('user')) {
            const messageId = currentStreamingMessage.getAttribute('data-message-id');
            if (messageId) {
                // Create container for user message
                const containerDiv = document.createElement('div');
                containerDiv.className = 'user-message-container';
                containerDiv.setAttribute('data-message-id', messageId);

                // Move the current message into the container
                currentStreamingMessage.parentNode.insertBefore(containerDiv, currentStreamingMessage);
                containerDiv.appendChild(currentStreamingMessage);

                // Add action buttons
                const userActionsDiv = createUserMessageActions(messageId, finalContent);
                containerDiv.appendChild(userActionsDiv);

                // Add double-click event for editing
                const editHandler = () => {
                    startEditingMessage(messageId, finalContent);
                };
                containerDiv.addEventListener('dblclick', editHandler);
                containerDiv._editHandler = editHandler;

                // Remove message ID from inner message div
                currentStreamingMessage.removeAttribute('data-message-id');
            }
        }
    }

    showTyping(false);
    currentStreamingMessage = null;
    scrollToBottomIfEnabled();
}

/**
 * Mark a message as final and add action buttons
 */
function markMessageAsFinal(messageId) {


    const message = document.querySelector(`[data-message-id="${messageId}"]`);
    if (!message) {
        console.warn('Message not found for final marking:', messageId);
        return;
    }

    // Remove the needs-actions attribute
    message.removeAttribute('data-needs-actions');

    // Add action buttons if it's an assistant message and doesn't already have them
    if (message.classList.contains('assistant') && !message.querySelector('.message-actions')) {
        const content = message.querySelector('.message-content');
        if (content) {
            const actionsDiv = createMessageActions(messageId, content.textContent || content.innerText);
            message.appendChild(actionsDiv);

        }
    }
}

// Cache for processed content to improve performance
const contentCache = new Map();

function processStreamContent(content) {
    // Check cache first for performance
    if (contentCache.has(content)) {
        return contentCache.get(content);
    }

    // Convert markdown to HTML using marked (configured globally)
    let htmlContent = marked.parse(content);

    // Sanitize the HTML
    const sanitizedContent = DOMPurify.sanitize(htmlContent, {
        ALLOWED_TAGS: [
            'p', 'br', 'strong', 'em', 'u', 's', 'code', 'pre',
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            'ul', 'ol', 'li', 'blockquote', 'table', 'thead', 'tbody', 'tr', 'th', 'td',
            'a', 'img', 'hr', 'div', 'span'
        ],
        ALLOWED_ATTR: ['href', 'src', 'alt', 'title', 'class', 'id']
    });

    // Cache the result (limit cache size to prevent memory issues)
    if (contentCache.size > 100) {
        const firstKey = contentCache.keys().next().value;
        contentCache.delete(firstKey);
    }
    contentCache.set(content, sanitizedContent);

    return sanitizedContent;
}

function processCodeBlocksInElement(element) {
    // Process code blocks
    const codeBlocks = element.querySelectorAll('pre');
    codeBlocks.forEach((pre, index) => {
        const code = pre.querySelector('code');
        if (!code) return;

        const codeId = 'code-' + Date.now() + '-' + index;
        const language = extractLanguage(code);
        const codeContent = code.textContent;

        const languageDisplay = language ? `<span class="code-block-language">${language}</span>` : '';

        const codeBlockHTML = `
                    <div class="code-block">
                        <div class="code-block-header">
                            <div class="code-block-title">
                                <i class="codicon codicon-chevron-right code-block-toggle" id="toggle-${codeId}" onclick="toggleCodeBlock('${codeId}', event)"></i>
                                <i class="codicon codicon-file-code"></i>
                                ${languageDisplay}
                            </div>
                            <div class="code-block-actions">
                                <button class="code-block-copy" onclick="copyCodeBlock('${codeId}')" title="Copy code">
                                    <i class="codicon codicon-copy"></i>
                                </button>
                            </div>
                        </div>
                        <div class="code-block-content" id="content-${codeId}">
                            <pre><code class="language-${language || 'text'}">${escapeHtml(codeContent)}</code></pre>
                        </div>
                    </div>
                `;

        pre.outerHTML = codeBlockHTML;
    });

    // Process tables - wrap them for horizontal scrolling
    const tables = element.querySelectorAll('table');
    tables.forEach(table => {
        if (!table.parentElement.classList.contains('table-wrapper')) {
            const wrapper = document.createElement('div');
            wrapper.className = 'table-wrapper';
            table.parentNode.insertBefore(wrapper, table);
            wrapper.appendChild(table);
        }
    });
}

function clearMessages() {
    chatContainer.innerHTML = `
                <div class="welcome-message">
                    <i class="codicon codicon-lightbulb welcome-icon"></i>
                    <div class="welcome-text">
                        <strong>Hello! I'm Augura Coder</strong><br>
                        Your AI programming assistant powered by advanced language models.<br>
                        Ask me anything about coding, and I'll help you out!
                    </div>
                </div>
            `;
}

function restoreSavedMessages(messages) {
    if (!messages || messages.length === 0) {
        return;
    }

    // Remove welcome message if it exists
    const welcomeMsg = chatContainer.querySelector('.welcome-message');
    if (welcomeMsg) {
        welcomeMsg.remove();
    }

    // Use document fragment for better performance
    const fragment = document.createDocumentFragment();

    // Add each saved message to fragment first
    messages.forEach(msg => {
        const messageDiv = createMessageElement(msg.role, msg.content, msg.id, msg.liked, msg.disliked);
        fragment.appendChild(messageDiv);
    });

    // Add all messages at once
    chatContainer.appendChild(fragment);

    // Restore UI state after messages are loaded
    setTimeout(() => {
        restoreUIState();
        updateScrollButtonPosition();
    }, 300);

    // Scroll to bottom if no saved scroll position
    requestAnimationFrame(() => {
        if (!sessionStorage.getItem('augura-ui-state')) {
            scrollToBottomIfEnabled();
        }
    });
}

// Optimized function to create message element without adding to DOM
function createMessageElement(role, content, messageId = null, liked = false, disliked = false) {
    if (role === 'user' && messageId) {
        // Create user message container
        const containerDiv = document.createElement('div');
        containerDiv.className = 'user-message-container';
        containerDiv.setAttribute('data-message-id', messageId);

        // Create message div
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.innerHTML = processStreamContent(content);
        messageDiv.appendChild(contentDiv);

        // Add action buttons for user messages
        const userActionsDiv = createUserMessageActions(messageId, content);

        // Add double-click event for editing
        const editHandler = () => {
            startEditingMessage(messageId, content);
        };
        containerDiv.addEventListener('dblclick', editHandler);
        containerDiv._editHandler = editHandler;

        // Append message and actions to container
        containerDiv.appendChild(messageDiv);
        containerDiv.appendChild(userActionsDiv);

        // Process code blocks and text direction after creating element
        setTimeout(() => {
            processCodeBlocksInElement(contentDiv);
            applySyntaxHighlighting(contentDiv);
            applyTextDirection(messageDiv, content);
        }, 0);

        return containerDiv;
    } else {
        // Create regular message for assistant
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;
        if (messageId) {
            messageDiv.setAttribute('data-message-id', messageId);
        }

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.innerHTML = processStreamContent(content);
        messageDiv.appendChild(contentDiv);

        // Add action buttons for assistant messages
        if (role === 'assistant' && messageId) {
            const actionsDiv = createMessageActions(messageId, content);
            messageDiv.appendChild(actionsDiv);

            // Set reaction states
            if (liked || disliked) {
                setTimeout(() => {
                    updateMessageReaction(messageId, liked, disliked);
                }, 0);
            }
        }

        // Process code blocks and text direction after creating element
        setTimeout(() => {
            processCodeBlocksInElement(contentDiv);
            applySyntaxHighlighting(contentDiv);
            applyTextDirection(messageDiv, content);
        }, 0);

        return messageDiv;
    }
}

function addMessageToChat(role, content, isStreaming = false, messageId = null) {
    if (role === 'user' && !isStreaming && messageId) {
        // Create user message container
        const containerDiv = document.createElement('div');
        containerDiv.className = 'user-message-container';
        containerDiv.setAttribute('data-message-id', messageId);

        // Create message div
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.innerHTML = processStreamContent(content);
        processCodeBlocksInElement(contentDiv);
        applySyntaxHighlighting(contentDiv);
        applyTextDirection(messageDiv, content);
        messageDiv.appendChild(contentDiv);

        // Add action buttons for user messages
        const userActionsDiv = createUserMessageActions(messageId, content);

        // Add double-click event for editing
        const editHandler = () => {
            startEditingMessage(messageId, content);
        };
        containerDiv.addEventListener('dblclick', editHandler);
        containerDiv._editHandler = editHandler;

        // Append message and actions to container
        containerDiv.appendChild(messageDiv);
        containerDiv.appendChild(userActionsDiv);

        chatContainer.appendChild(containerDiv);
        return containerDiv;
    } else {
        // Create regular message for assistant or streaming
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}`;
        if (messageId) {
            messageDiv.setAttribute('data-message-id', messageId);
        }

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';

        if (isStreaming) {
            contentDiv.innerHTML = '';
        } else {
            contentDiv.innerHTML = processStreamContent(content);
            processCodeBlocksInElement(contentDiv);
            applySyntaxHighlighting(contentDiv);
            applyTextDirection(messageDiv, content);
        }

        messageDiv.appendChild(contentDiv);

        // Add action buttons for assistant messages
        if (role === 'assistant' && !isStreaming) {
            const actionsDiv = createMessageActions(messageId, content);
            messageDiv.appendChild(actionsDiv);
        }

        chatContainer.appendChild(messageDiv);
        return messageDiv;
    }
}

function createMessageActions(messageId, content) {
    const actionsDiv = document.createElement('div');
    actionsDiv.className = 'message-actions';

    // Regenerate button
    const regenerateBtn = document.createElement('button');
    regenerateBtn.className = 'message-action-btn';
    regenerateBtn.innerHTML = '<span class="codicon codicon-refresh"></span>';
    regenerateBtn.title = 'إعادة توليد';
    regenerateBtn.onclick = () => {
        vscode.postMessage({
            type: 'regenerateMessage',
            messageId: messageId
        });
    };

    // Like button
    const likeBtn = document.createElement('button');
    likeBtn.className = 'message-action-btn like-btn';
    likeBtn.innerHTML = '<span class="codicon codicon-thumbsup"></span>';
    likeBtn.title = 'إعجاب';
    likeBtn.onclick = () => {
        const isActive = likeBtn.classList.contains('active');
        vscode.postMessage({
            type: 'likeMessage',
            messageId: messageId,
            liked: !isActive
        });
    };

    // Dislike button
    const dislikeBtn = document.createElement('button');
    dislikeBtn.className = 'message-action-btn dislike-btn';
    dislikeBtn.innerHTML = '<span class="codicon codicon-thumbsdown"></span>';
    dislikeBtn.title = 'عدم إعجاب';
    dislikeBtn.onclick = () => {
        const isActive = dislikeBtn.classList.contains('active');
        vscode.postMessage({
            type: 'dislikeMessage',
            messageId: messageId,
            disliked: !isActive
        });
    };

    // Copy button
    const copyBtn = document.createElement('button');
    copyBtn.className = 'message-action-btn';
    copyBtn.innerHTML = '<span class="codicon codicon-copy"></span>';
    copyBtn.title = 'نسخ';
    copyBtn.onclick = () => {
        vscode.postMessage({
            type: 'copyMessage',
            content: content
        });
    };

    actionsDiv.appendChild(regenerateBtn);
    actionsDiv.appendChild(likeBtn);
    actionsDiv.appendChild(dislikeBtn);
    actionsDiv.appendChild(copyBtn);

    return actionsDiv;
}

function createUserMessageActions(messageId, content) {
    const actionsDiv = document.createElement('div');
    actionsDiv.className = 'user-message-actions';

    // Copy button
    const copyBtn = document.createElement('button');
    copyBtn.className = 'user-action-btn';
    copyBtn.innerHTML = '<span class="codicon codicon-copy"></span>';
    copyBtn.title = 'نسخ';
    copyBtn.onclick = () => {
        vscode.postMessage({
            type: 'copyMessage',
            content: content
        });
    };

    // Edit button
    const editBtn = document.createElement('button');
    editBtn.className = 'user-action-btn edit-btn';
    editBtn.innerHTML = '<span class="codicon codicon-edit"></span>';
    editBtn.title = 'تعديل';
    editBtn.onclick = () => {
        startEditingMessage(messageId, content);
    };

    actionsDiv.appendChild(copyBtn);
    actionsDiv.appendChild(editBtn);

    return actionsDiv;
}

// Message editing functions
function startEditingMessage(messageId, content) {
    const containerElement = document.querySelector(`[data-message-id="${messageId}"]`);
    if (!containerElement) return;

    const messageDiv = containerElement.querySelector('.message');
    const contentDiv = messageDiv ? messageDiv.querySelector('.message-content') : null;
    const actionsDiv = containerElement.querySelector('.user-message-actions');

    if (!contentDiv) return;

    // Hide original content and actions
    contentDiv.style.display = 'none';
    if (actionsDiv) actionsDiv.style.display = 'none';

    // Create edit interface
    const editContainer = document.createElement('div');
    editContainer.className = 'message-edit-container';

    const editInput = document.createElement('textarea');
    editInput.className = 'message-edit-input';
    editInput.value = content;
    editInput.placeholder = 'تعديل الرسالة...';

    const editActions = document.createElement('div');
    editActions.className = 'message-edit-actions';

    const saveBtn = document.createElement('button');
    saveBtn.className = 'edit-action-btn save';
    saveBtn.innerHTML = '<i class="codicon codicon-check"></i> حفظ';
    saveBtn.onclick = () => {
        saveEditedMessage(messageId, editInput.value.trim());
    };

    const cancelBtn = document.createElement('button');
    cancelBtn.className = 'edit-action-btn cancel';
    cancelBtn.innerHTML = '<i class="codicon codicon-close"></i> إلغاء';
    cancelBtn.onclick = () => {
        cancelEditingMessage(messageId);
    };

    editActions.appendChild(cancelBtn);
    editActions.appendChild(saveBtn);
    editContainer.appendChild(editInput);
    editContainer.appendChild(editActions);

    // Insert edit interface
    containerElement.appendChild(editContainer);

    // Focus on input and select all text
    editInput.focus();
    editInput.select();

    // Handle Enter key (Ctrl+Enter to save)
    editInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && e.ctrlKey) {
            e.preventDefault();
            saveEditedMessage(messageId, editInput.value.trim());
        } else if (e.key === 'Escape') {
            e.preventDefault();
            cancelEditingMessage(messageId);
        }
    });
}

function saveEditedMessage(messageId, newContent) {
    if (!newContent) {
        alert('لا يمكن أن تكون الرسالة فارغة');
        return;
    }

    // Send edit message to backend
    vscode.postMessage({
        type: 'editMessage',
        messageId: messageId,
        newContent: newContent
    });
}

function cancelEditingMessage(messageId) {
    const containerElement = document.querySelector(`[data-message-id="${messageId}"]`);
    if (!containerElement) return;

    const messageDiv = containerElement.querySelector('.message');
    const contentDiv = messageDiv ? messageDiv.querySelector('.message-content') : null;
    const actionsDiv = containerElement.querySelector('.user-message-actions');
    const editContainer = containerElement.querySelector('.message-edit-container');

    // Show original content and actions
    if (contentDiv) contentDiv.style.display = 'block';
    if (actionsDiv) actionsDiv.style.display = 'flex';

    // Remove edit interface
    if (editContainer) editContainer.remove();
}

function handleEditMessageComplete(messageId, newContent) {
    const containerElement = document.querySelector(`[data-message-id="${messageId}"]`);
    if (!containerElement) return;

    const messageDiv = containerElement.querySelector('.message');
    const contentDiv = messageDiv ? messageDiv.querySelector('.message-content') : null;
    const actionsDiv = containerElement.querySelector('.user-message-actions');
    const editContainer = containerElement.querySelector('.message-edit-container');

    // Update content
    if (contentDiv) {
        contentDiv.innerHTML = processStreamContent(newContent);
        processCodeBlocksInElement(contentDiv);
        applySyntaxHighlighting(contentDiv);
        applyTextDirection(messageDiv, newContent);
        contentDiv.style.display = 'block';
    }

    // Show actions
    if (actionsDiv) actionsDiv.style.display = 'flex';

    // Remove edit interface
    if (editContainer) editContainer.remove();

    // Update the double-click event with new content
    const newClickHandler = () => {
        startEditingMessage(messageId, newContent);
    };

    // Remove old event listener and add new one
    containerElement.removeEventListener('dblclick', containerElement._editHandler);
    containerElement.addEventListener('dblclick', newClickHandler);
    containerElement._editHandler = newClickHandler;
}

// Enhanced retry notification system
let retryNotificationElement = null;

function showRetryNotification(attempt, maxAttempts, delaySeconds, errorType) {
    // Remove existing notification if any
    if (retryNotificationElement) {
        retryNotificationElement.remove();
        retryNotificationElement = null;
    }

    // Create notification element
    retryNotificationElement = document.createElement('div');
    retryNotificationElement.className = 'retry-notification';
    retryNotificationElement.innerHTML = `
        <div class="retry-content">
            <div class="retry-header">
                <i class="codicon codicon-warning"></i>
                <span class="retry-title">${errorType} Error</span>
            </div>
            <div class="retry-message">
                Retrying automatically in <span class="countdown-timer">${delaySeconds}</span> seconds...
            </div>
            <div class="retry-progress">
                <span class="retry-attempt">Attempt ${attempt} of ${maxAttempts}</span>
                <div class="retry-progress-bar">
                    <div class="retry-progress-fill" style="width: ${(attempt / maxAttempts) * 100}%"></div>
                </div>
            </div>
        </div>
    `;

    // Add to chat container
    chatContainer.appendChild(retryNotificationElement);
    scrollToBottomIfEnabled();

    // Start countdown
    startRetryCountdown(delaySeconds);
}

function startRetryCountdown(seconds) {
    let remaining = seconds;
    const timerElement = retryNotificationElement?.querySelector('.countdown-timer');

    const countdown = setInterval(() => {
        remaining--;
        if (timerElement) {
            timerElement.textContent = remaining;
        }

        if (remaining <= 0) {
            clearInterval(countdown);
            // Remove notification after countdown
            setTimeout(() => {
                if (retryNotificationElement) {
                    retryNotificationElement.remove();
                    retryNotificationElement = null;
                }
            }, 1000);
        }
    }, 1000);
}

// Legacy function for compatibility
function showRetryCountdown(remainingTime, attempt, maxAttempts) {
    showRetryNotification(attempt, maxAttempts, remainingTime, 'API');
}

function updateRetryCountdown(remainingTime) {
    // Legacy function - now handled by startRetryCountdown
}

// Error notification system
function showErrorNotification(errorType, message, status, retryable) {
    const errorElement = document.createElement('div');
    errorElement.className = 'error-notification';

    const errorTypeText = errorType.replace('_', ' ').toUpperCase();
    const statusText = status ? ` (${status})` : '';
    const retryableText = retryable ? 'This error will be retried automatically.' : 'Please check your configuration and try again.';

    errorElement.innerHTML = `
        <div class="error-content">
            <div class="error-header">
                <i class="codicon codicon-error"></i>
                <span class="error-title">${errorTypeText}${statusText}</span>
            </div>
            <div class="error-message">${message}</div>
            <div class="error-footer">
                <span class="error-hint">${retryableText}</span>
            </div>
        </div>
    `;

    // Add to chat container
    chatContainer.appendChild(errorElement);
    scrollToBottomIfEnabled();

    // Auto-remove after 10 seconds for retryable errors, 30 seconds for non-retryable
    const autoRemoveDelay = retryable ? 10000 : 30000;
    setTimeout(() => {
        if (errorElement && errorElement.parentNode) {
            errorElement.remove();
        }
    }, autoRemoveDelay);
}

function showRetryThinking() {
    // Remove countdown
    if (retryCountdownElement) {
        retryCountdownElement.remove();
        retryCountdownElement = null;
    }

    // Show thinking indicator
    showTyping(true);
}

function removeMessage(messageId) {
    const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
    if (messageElement) {
        messageElement.remove();
    }
}

function updateMessageReaction(messageId, liked, disliked) {
    const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
    if (messageElement) {
        const likeBtn = messageElement.querySelector('.like-btn');
        const dislikeBtn = messageElement.querySelector('.dislike-btn');

        if (likeBtn) {
            likeBtn.classList.toggle('active', liked);
        }
        if (dislikeBtn) {
            dislikeBtn.classList.toggle('active', disliked);
        }
    }
}

function showTyping(isTyping) {
    typingIndicator.style.display = isTyping ? 'block' : 'none';
    if (isTyping) {
        // Move typing indicator to end of chat
        chatContainer.appendChild(typingIndicator);
        scrollToBottomIfEnabled();
    }
}

function sendMessage() {
    const message = messageInput.value.trim();
    if (message) {
        // Force scroll to bottom when sending a new message
        forceScrollToBottom();

        // Show user message immediately
        addMessageToChat('user', message, false, Date.now().toString());

        // Send to extension with agent context
        vscode.postMessage({
            type: 'sendMessage',
            message: message,
            agentMode: true,
            context: {
                currentTask: currentTask,
                timestamp: new Date().toISOString()
            }
        });

        messageInput.value = '';
        adjustTextareaHeight();
        updateSendButton();

        // Show thinking indicator
        showTyping(true);
    }
}

function updateSendButton() {
    const sendBtn = document.querySelector('.send-btn');
    const hasText = messageInput.value.trim().length > 0;
    sendBtn.disabled = !hasText;
}

function clearChat() {
    vscode.postMessage({
        type: 'clearChat'
    });
}

function adjustTextareaHeight() {
    messageInput.style.height = 'auto';
    messageInput.style.height = Math.min(messageInput.scrollHeight, 100) + 'px';
}

// Event listeners
messageInput.addEventListener('keydown', (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        if (messageInput.value.trim()) {
            sendMessage();
        }
    }
});

messageInput.addEventListener('input', () => {
    adjustTextareaHeight();
    updateSendButton();
});

// Initialize send button state
updateSendButton();

// Scroll management - detect user scrolling
chatContainer.addEventListener('scroll', () => {
    if (scrollTimeout) {
        clearTimeout(scrollTimeout);
    }

    userIsScrolling = true;

    // Check if user scrolled to bottom
    const isAtBottom = chatContainer.scrollTop + chatContainer.clientHeight >= chatContainer.scrollHeight - 10;
    autoScrollEnabled = isAtBottom;

    // Show/hide scroll to bottom button
    if (isAtBottom) {
        scrollToBottomBtn.classList.remove('visible');
    } else {
        scrollToBottomBtn.classList.add('visible');
    }

    // Reset user scrolling flag after a delay
    scrollTimeout = setTimeout(() => {
        userIsScrolling = false;
        // Save scroll position
        uiState.scrollPosition = chatContainer.scrollTop;
    }, 150);
});

// Detect wheel/touch scrolling to disable auto-scroll
chatContainer.addEventListener('wheel', () => {
    userIsScrolling = true;
    autoScrollEnabled = false;
});

chatContainer.addEventListener('touchstart', () => {
    userIsScrolling = true;
    autoScrollEnabled = false;
});

// Smart scroll function that respects user interaction
function scrollToBottomIfEnabled() {
    if (autoScrollEnabled && !userIsScrolling) {
        // Add small delay for more natural scrolling
        setTimeout(() => {
            if (autoScrollEnabled && !userIsScrolling) {
                chatContainer.scrollTo({
                    top: chatContainer.scrollHeight,
                    behavior: 'smooth'
                });
            }
        }, 50);
    }
}

// Function to force scroll to bottom (for new messages)
function forceScrollToBottom() {
    autoScrollEnabled = true;
    userIsScrolling = false;

    // Add visual feedback to button
    scrollToBottomBtn.style.transform = 'translateX(-50%) scale(0.9)';

    // Smooth scroll to bottom
    chatContainer.scrollTo({
        top: chatContainer.scrollHeight,
        behavior: 'smooth'
    });

    // Hide button and reset transform after scroll
    setTimeout(() => {
        scrollToBottomBtn.classList.remove('visible');
        scrollToBottomBtn.style.transform = 'translateX(-50%) scale(1)';
    }, 300);
}

// Function to update scroll button position based on files changed panel
function updateScrollButtonPosition() {
    const filesSection = document.getElementById('filesChangedSection');
    const isExpanded = filesSection && filesSection.querySelector('.files-changed-content').classList.contains('expanded');

    if (isExpanded) {
        // Files panel is open - move button up
        scrollToBottomBtn.style.bottom = '210px'; // Adjust based on files panel height
    } else {
        // Files panel is closed - normal position
        scrollToBottomBtn.style.bottom = '140px';
    }
}

// Files changed functionality
function toggleFilesChanged(event) {
    // Prevent event bubbling if called from icon click
    if (event) {
        event.stopPropagation();
    }

    const content = document.getElementById('filesChangedContent');
    const toggle = document.getElementById('filesToggle');

    // Check if it's in default collapsed state (no classes) or explicitly collapsed
    const isCollapsed = !content.classList.contains('expanded');

    if (isCollapsed) {
        // Expand - show content, arrow points down
        content.classList.add('expanded');
        content.classList.remove('collapsed');
        toggle.classList.remove('collapsed');
        // Change icon to chevron-down (pointing down when expanded)
        toggle.classList.remove('codicon-chevron-right');
        toggle.classList.add('codicon-chevron-down');
        // Save state
        uiState.filesChangedExpanded = true;
    } else {
        // Collapse - hide content, arrow points right
        content.classList.remove('expanded');
        content.classList.add('collapsed');
        toggle.classList.add('collapsed');
        // Change icon to chevron-right (pointing right when collapsed)
        toggle.classList.remove('codicon-chevron-down');
        toggle.classList.add('codicon-chevron-right');
        // Save state
        uiState.filesChangedExpanded = false;
    }

    // Update scroll to bottom button position
    updateScrollButtonPosition();

    // Save state immediately
    saveUIState();
}

// Get file icon based on extension
function getFileIcon(extension) {
    const iconMap = {
        'js': 'codicon-symbol-method',
        'ts': 'codicon-symbol-method',
        'html': 'codicon-symbol-structure',
        'css': 'codicon-symbol-color',
        'json': 'codicon-symbol-object',
        'md': 'codicon-symbol-text',
        'py': 'codicon-symbol-method',
        'java': 'codicon-symbol-class',
        'cpp': 'codicon-symbol-method',
        'c': 'codicon-symbol-method',
        'php': 'codicon-symbol-method',
        'rb': 'codicon-symbol-method',
        'go': 'codicon-symbol-method',
        'rs': 'codicon-symbol-method',
        'xml': 'codicon-symbol-structure',
        'yml': 'codicon-symbol-object',
        'yaml': 'codicon-symbol-object',
        'txt': 'codicon-symbol-text',
        'log': 'codicon-symbol-text'
    };

    return iconMap[extension] || 'codicon-symbol-file';
}



// Helper functions for code blocks
function extractLanguage(codeElement) {
    const className = codeElement.className;
    const match = className.match(/language-(\w+)/);
    let language = match ? match[1] : null;

    // Comprehensive language mapping for all supported languages
    const languageMap = {
        // JavaScript family
        'js': 'javascript',
        'jsx': 'jsx',
        'ts': 'typescript',
        'tsx': 'tsx',
        'node': 'javascript',
        'nodejs': 'javascript',
        // Python family
        'py': 'python',
        'python3': 'python',
        'py3': 'python',
        // Web technologies
        'html': 'markup',
        'htm': 'markup',
        'xml': 'markup',
        'svg': 'markup',
        'mathml': 'markup',
        'ssml': 'markup',
        'atom': 'markup',
        'rss': 'markup',
        'xhtml': 'markup',

        // Stylesheets
        'css': 'css',
        'scss': 'scss',
        'sass': 'sass',
        'less': 'less',
        'stylus': 'stylus',

        // Shell/Command line
        'sh': 'bash',
        'shell': 'bash',
        'bash': 'bash',
        'zsh': 'bash',
        'fish': 'bash',
        'powershell': 'powershell',
        'ps1': 'powershell',
        'cmd': 'batch',
        'bat': 'batch',
        'batch': 'batch',

        // Data formats
        'json': 'json',
        'json5': 'json5',
        'jsonp': 'jsonp',
        'yaml': 'yaml',
        'yml': 'yaml',
        'toml': 'toml',
        'xml': 'markup',
        'csv': 'csv',

        // C family
        'c': 'c',
        'cpp': 'cpp',
        'c++': 'cpp',
        'cxx': 'cpp',
        'cc': 'cpp',
        'h': 'c',
        'hpp': 'cpp',
        'cs': 'csharp',
        'csharp': 'csharp',

        // Java family
        'java': 'java',
        'scala': 'scala',
        'kotlin': 'kotlin',
        'kt': 'kotlin',
        'groovy': 'groovy',

        // .NET
        'vb': 'vbnet',
        'vbnet': 'vbnet',
        'fsharp': 'fsharp',
        'fs': 'fsharp',

        // Mobile
        'swift': 'swift',
        'objc': 'objectivec',
        'objective-c': 'objectivec',
        'dart': 'dart',

        // Functional
        'haskell': 'haskell',
        'hs': 'haskell',
        'elm': 'elm',
        'clojure': 'clojure',
        'clj': 'clojure',
        'erlang': 'erlang',
        'elixir': 'elixir',
        'ocaml': 'ocaml',
        'reason': 'reason',
        'purescript': 'purescript',

        // Systems
        'rust': 'rust',
        'rs': 'rust',
        'go': 'go',
        'golang': 'go',
        'zig': 'zig',
        'nim': 'nim',
        'crystal': 'crystal',

        // Scripting
        'ruby': 'ruby',
        'rb': 'ruby',
        'php': 'php',
        'perl': 'perl',
        'pl': 'perl',
        'lua': 'lua',

        // Database
        'sql': 'sql',
        'mysql': 'sql',
        'postgresql': 'sql',
        'sqlite': 'sql',
        'plsql': 'plsql',

        // Markup/Documentation
        'markdown': 'markdown',
        'md': 'markdown',
        'tex': 'latex',
        'latex': 'latex',
        'rst': 'rest',
        'asciidoc': 'asciidoc',
        'adoc': 'asciidoc',

        // Configuration
        'ini': 'ini',
        'conf': 'ini',
        'config': 'ini',
        'properties': 'properties',
        'dockerfile': 'docker',
        'docker': 'docker',
        'makefile': 'makefile',
        'make': 'makefile',
        'cmake': 'cmake',
        'nginx': 'nginx',
        'apache': 'apacheconf',

        // Assembly
        'asm': 'nasm',
        'nasm': 'nasm',
        'assembly': 'nasm',

        // Other popular languages
        'r': 'r',
        'matlab': 'matlab',
        'julia': 'julia',
        'fortran': 'fortran',
        'cobol': 'cobol',
        'pascal': 'pascal',
        'ada': 'ada',
        'prolog': 'prolog',
        'scheme': 'scheme',
        'lisp': 'lisp',
        'smalltalk': 'smalltalk',

        // Game development
        'gdscript': 'gdscript',
        'hlsl': 'hlsl',
        'glsl': 'glsl',

        // Blockchain
        'solidity': 'solidity',
        'sol': 'solidity',

        // Query languages
        'graphql': 'graphql',
        'sparql': 'sparql',

        // Template engines
        'handlebars': 'handlebars',
        'hbs': 'handlebars',
        'mustache': 'handlebars',
        'twig': 'twig',
        'smarty': 'smarty',
        'pug': 'pug',
        'jade': 'pug',
        'ejs': 'ejs',

        // Version control
        'diff': 'diff',
        'patch': 'diff',
        'git': 'git',

        // Misc
        'regex': 'regex',
        'regexp': 'regex',
        'log': 'log',
        'text': 'text',
        'txt': 'text',
        'plain': 'text'
    };

    if (language && languageMap[language]) {
        language = languageMap[language];
    }

    return language;
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Function to apply syntax highlighting to dynamically added content
function applySyntaxHighlighting(element) {
    if (!window.Prism) {
        // If Prism is not loaded yet, try again after a short delay
        setTimeout(() => applySyntaxHighlighting(element), 100);
        return;
    }

    // Function to highlight a single code element
    function highlightCodeElement(code, isInline = false) {
        const language = extractLanguage(code);

        if (!language || language === 'text' || language === 'plain') {
            return; // Skip highlighting for plain text
        }

        // Check if already highlighted
        if (code.classList.contains('prism-highlighted')) {
            return;
        }

        // If language is available, highlight immediately
        if (window.Prism.languages[language]) {
            try {
                const highlighted = window.Prism.highlight(code.textContent, window.Prism.languages[language], language);
                code.innerHTML = highlighted;
                code.classList.add('prism-highlighted');

            } catch (e) {
                console.warn(`Failed to highlight ${language} code:`, e);
            }
        } else {
            // Language not loaded yet, try to load it with autoloader


            // Mark as pending to avoid multiple load attempts
            code.classList.add('prism-loading');

            // Use Prism's autoloader to load the language
            if (window.Prism.plugins && window.Prism.plugins.autoloader) {
                // Create a temporary element to trigger autoloader
                const tempElement = document.createElement('code');
                tempElement.className = `language-${language}`;
                tempElement.textContent = code.textContent;
                tempElement.style.display = 'none';
                document.body.appendChild(tempElement);

                // Try to highlight with autoloader
                try {
                    window.Prism.highlightElement(tempElement);
                } catch (e) {
                    console.warn(`Autoloader failed for ${language}:`, e);
                }

                // Clean up temp element
                document.body.removeChild(tempElement);

                // Check if language is now available and retry
                setTimeout(() => {
                    if (window.Prism.languages[language] && !code.classList.contains('prism-highlighted')) {
                        code.classList.remove('prism-loading');
                        highlightCodeElement(code, isInline);
                    } else {
                        // If still not available after timeout, mark as failed and remove loading class
                        code.classList.remove('prism-loading');
                        console.warn(`Language ${language} could not be loaded`);
                    }
                }, 1000);
            }
        }
    }

    // Highlight code blocks in .code-block-content
    const codeBlocks = element.querySelectorAll('.code-block-content code');
    codeBlocks.forEach(code => highlightCodeElement(code, false));

    // Highlight inline code elements (not in pre tags)
    const inlineCodes = element.querySelectorAll('code:not(pre code):not(.code-block-content code)');
    inlineCodes.forEach(code => highlightCodeElement(code, true));
}

// Function to re-highlight all existing code blocks
function rehighlightAllCode() {
    if (!window.Prism) {
        console.warn('Prism.js not loaded yet');
        return;
    }



    // Remove existing highlighting classes
    document.querySelectorAll('code.prism-highlighted, code.prism-loading').forEach(code => {
        code.classList.remove('prism-highlighted', 'prism-loading');
    });

    // Re-apply highlighting to all code blocks
    applySyntaxHighlighting(document.body);


}


// Text direction detection
function detectTextDirection(text) {
    // Remove code blocks from direction detection
    const textWithoutCode = text.replace(/```[\\s\\S]*?```/g, '')
        .replace(/`[^`]+`/g, '')
        .replace(/<pre[\\s\\S]*?<\/pre>/gi, '')
        .replace(/<code[\\s\\S]*?<\/code>/gi, '');

    // Use bidi-js for direction detection
    if (typeof Bidi !== 'undefined') {
        try {
            const bidiText = new Bidi(textWithoutCode, { dir: 'auto' });
            return bidiText.dir;
        } catch (e) {
            // Fallback to simple detection
            return detectSimpleDirection(textWithoutCode);
        }
    }

    return detectSimpleDirection(textWithoutCode);
}

function detectSimpleDirection(text) {
    // Simple RTL detection for Arabic, Hebrew, Persian, etc.
    const rtlChars = /[\u0590-\u05FF\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
    const ltrChars = /[A-Za-z]/;

    const rtlCount = (text.match(rtlChars) || []).length;
    const ltrCount = (text.match(ltrChars) || []).length;

    if (rtlCount > ltrCount) return 'rtl';
    if (ltrCount > rtlCount) return 'ltr';
    return 'auto';
}

function applyTextDirection(element, content) {
    const direction = detectTextDirection(content);
    element.setAttribute('dir', direction);
}

function toggleCodeBlock(codeId, event) {
    // Prevent event bubbling if called from icon click
    if (event) {
        event.stopPropagation();
    }

    const content = document.getElementById('content-' + codeId);
    const toggle = document.getElementById('toggle-' + codeId);

    // Check if it's in default collapsed state (no classes) or explicitly collapsed
    const isCollapsed = !content.classList.contains('expanded');

    if (isCollapsed) {
        // Expand
        content.classList.add('expanded');
        content.classList.remove('collapsed');
        toggle.classList.remove('collapsed');
        // Change icon to chevron-down
        toggle.classList.remove('codicon-chevron-right');
        toggle.classList.add('codicon-chevron-down');
        // Save state
        uiState.expandedCodeBlocks.add(codeId);
    } else {
        // Collapse
        content.classList.remove('expanded');
        content.classList.add('collapsed');
        toggle.classList.add('collapsed');
        // Change icon to chevron-right
        toggle.classList.remove('codicon-chevron-down');
        toggle.classList.add('codicon-chevron-right');
        // Save state
        uiState.expandedCodeBlocks.delete(codeId);
    }

    // Save state immediately
    saveUIState();
}

function copyCodeBlock(codeId) {
    const content = document.getElementById('content-' + codeId);
    const code = content.querySelector('code').textContent;

    navigator.clipboard.writeText(code).then(() => {
        // Show feedback
        const copyBtn = content.parentElement.querySelector('.code-block-copy');
        const originalIcon = copyBtn.innerHTML;
        copyBtn.innerHTML = '<i class="codicon codicon-check"></i>';
        copyBtn.style.color = '#4ade80';

        setTimeout(() => {
            copyBtn.innerHTML = originalIcon;
            copyBtn.style.color = '';
        }, 1000);
    }).catch(err => {
        console.error('Failed to copy code:', err);
    });
}

// Threads management functions
function toggleThreadsPanel() {
    const header = document.querySelector('.header');
    const threadsContainer = document.querySelector('.threads-container');
    const toggleBtn = document.getElementById('threadsToggleBtn');
    const chevron = document.getElementById('threadsChevron');

    threadsPanelOpen = !threadsPanelOpen;

    if (threadsPanelOpen) {
        // Show threads panel
        header.classList.remove('collapsed');
        header.classList.add('expanded');
        threadsContainer.classList.add('show');
        toggleBtn.classList.add('active');
        chevron.classList.remove('codicon-chevron-right');
        chevron.classList.add('codicon-chevron-down');

        // Request threads list from extension
        vscode.postMessage({ type: 'requestThreadsList' });
    } else {
        // Hide threads panel
        header.classList.remove('expanded');
        header.classList.add('collapsed');
        threadsContainer.classList.remove('show');
        toggleBtn.classList.remove('active');
        chevron.classList.remove('codicon-chevron-down');
        chevron.classList.add('codicon-chevron-right');
    }
}

function requestThreadsList() {
    // Request threads list from extension
    vscode.postMessage({ type: 'requestThreadsList' });
}

function updateThreadsList(threads) {
    currentThreads = threads;
    const todayContainer = document.getElementById('todayThreads');
    const weekContainer = document.getElementById('weekThreads');

    // Clear existing content
    todayContainer.innerHTML = '';
    weekContainer.innerHTML = '';

    if (!threads || threads.length === 0) {
        todayContainer.innerHTML = '<div class="no-threads">لا توجد محادثات</div>';
        return;
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    threads.forEach(thread => {
        const threadDate = new Date(thread.lastActivity);
        const threadDay = new Date(threadDate.getFullYear(), threadDate.getMonth(), threadDate.getDate());

        const threadElement = createThreadElement(thread);

        if (threadDay.getTime() === today.getTime()) {
            todayContainer.appendChild(threadElement);
        } else if (threadDate >= weekAgo) {
            weekContainer.appendChild(threadElement);
        }
    });

    // Hide sections if empty
    if (todayContainer.children.length === 0) {
        todayContainer.innerHTML = '<div class="no-threads">لا توجد محادثات اليوم</div>';
    }
    if (weekContainer.children.length === 0) {
        weekContainer.innerHTML = '<div class="no-threads">لا توجد محادثات هذا الأسبوع</div>';
    }
}

function createThreadElement(thread) {
    const threadItem = document.createElement('div');
    threadItem.className = `thread-item ${thread.isActive ? 'active' : ''}`;
    threadItem.onclick = () => switchToThread(thread.id);

    // Add accessibility attributes
    threadItem.setAttribute('role', 'button');
    threadItem.setAttribute('tabindex', '0');
    threadItem.setAttribute('aria-label', `محادثة: ${thread.title}. آخر نشاط: ${new Date(thread.lastActivity).toLocaleString('ar-SA')}`);

    // Add keyboard navigation
    threadItem.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            switchToThread(thread.id);
        }
    });

    // Format time
    const lastActivity = new Date(thread.lastActivity);
    const now = new Date();
    const diffMs = now - lastActivity;
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    let timeText;
    if (diffMins < 1) {
        timeText = 'الآن';
    } else if (diffMins < 60) {
        timeText = `${diffMins} د`;
    } else if (diffHours < 24) {
        timeText = `${diffHours} س`;
    } else if (diffDays < 7) {
        timeText = `${diffDays} ي`;
    } else {
        timeText = lastActivity.toLocaleDateString('ar-SA', { month: 'short', day: 'numeric' });
    }

    // Get message count
    const messageCount = thread.messageCount || 0;

    threadItem.innerHTML = `
        <div class="thread-info">
            <div class="thread-title">${thread.title}</div>
            <div class="thread-preview">${thread.preview}</div>
            <div class="thread-meta">
                <span class="thread-time">${timeText}</span>
                ${messageCount > 0 ? `<span class="thread-message-count">${messageCount}</span>` : ''}
            </div>
        </div>
        <div class="thread-actions">
            <button class="thread-action-btn delete" onclick="deleteThread('${thread.id}', event)" title="حذف المحادثة" aria-label="حذف محادثة ${thread.title}">
                <i class="codicon codicon-trash"></i>
            </button>
        </div>
    `;

    return threadItem;
}

function switchToThread(threadId) {
    vscode.postMessage({
        type: 'switchThread',
        threadId: threadId
    });
}

function deleteThread(threadId, event) {
    event.stopPropagation(); // Prevent thread switch
    vscode.postMessage({
        type: 'deleteThread',
        threadId: threadId
    });
}

function createNewChat() {
    vscode.postMessage({
        type: 'createNewThread'
    });
}

function handleThreadSwitch(threadId) {
    // Update UI to reflect thread switch
    currentThreads.forEach(thread => {
        thread.isActive = thread.id === threadId;
    });
    updateThreadsList(currentThreads);
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Start with collapsed state
    const header = document.querySelector('.header');
    header.classList.add('collapsed');
});

// Save state before page unload
window.addEventListener('beforeunload', () => {
    saveUIState();
});

// Save state when visibility changes (tab switch, minimize, etc.)
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        saveUIState();
    }
});

// Make functions global for onclick handlers
window.toggleFilesChanged = toggleFilesChanged;

// ===== AGENT MODE FUNCTIONS =====

/**
 * Handle agent actions
 */
function handleAgentAction(action, data) {


    switch (action) {
        case 'fileRead':
            showAgentMessage(`📖 Reading file: ${data.file}`, 'action');
            break;

        case 'codeAnalysis':
            showAgentMessage(`🔍 Analyzing code in: ${data.file}`, 'action');
            break;
        case 'codeGeneration':
            showAgentMessage(`🚀 Generating code for: ${data.description}`, 'action');
            break;
        case 'codeRefactor':
            showAgentMessage(`🔧 Refactoring code in: ${data.file}`, 'action');
            break;
        case 'commandExecute':
            showAgentMessage(`⚡ Executing command: ${data.command}`, 'action');
            break;
        case 'thinking':
            showAgentMessage(`🤔 ${data.message}`, 'thinking');
            break;
        default:

    }
}

/**
 * Update current task display
 */
function updateCurrentTask(task) {
    currentTask = task;

    if (task) {
        showAgentMessage(`📋 Current task: ${task.description}`, 'task');

        // Update UI to show current task
        const taskElement = document.createElement('div');
        taskElement.className = 'current-task';
        taskElement.innerHTML = `
            <div class="task-header">
                <i class="codicon codicon-checklist"></i>
                <span>Current Task</span>
            </div>
            <div class="task-content">
                <div class="task-title">${task.title || 'Untitled Task'}</div>
                <div class="task-description">${task.description}</div>
                <div class="task-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${task.progress || 0}%"></div>
                    </div>
                    <span class="progress-text">${task.progress || 0}%</span>
                </div>
            </div>
        `;

        // Add to chat container
        chatContainer.appendChild(taskElement);
        scrollToBottomIfEnabled();
    }
}



/**
 * Show agent-specific messages
 */
function showAgentMessage(message, type = 'info') {
    const messageElement = document.createElement('div');
    messageElement.className = `agent-message agent-${type}`;

    const icon = getAgentIcon(type);
    const timestamp = new Date().toLocaleTimeString();

    messageElement.innerHTML = `
        <div class="agent-message-content">
            <span class="agent-icon">${icon}</span>
            <span class="agent-text">${message}</span>
            <span class="agent-timestamp">${timestamp}</span>
        </div>
    `;

    chatContainer.appendChild(messageElement);
    scrollToBottomIfEnabled();

    // Auto-remove action messages after 5 seconds
    if (type === 'action' || type === 'thinking') {
        setTimeout(() => {
            if (messageElement.parentNode) {
                messageElement.remove();
            }
        }, 5000);
    }
}

/**
 * Get icon for agent message type
 */
function getAgentIcon(type) {
    const icons = {
        'action': '⚡',
        'thinking': '🤔',
        'task': '📋',
        'file-change': '📝',
        'success': '✅',
        'error': '❌',
        'warning': '⚠️',
        'info': 'ℹ️'
    };

    return icons[type] || 'ℹ️';
}



window.toggleCodeBlock = toggleCodeBlock;
window.copyCodeBlock = copyCodeBlock;
window.forceScrollToBottom = forceScrollToBottom;
window.toggleThreadsPanel = toggleThreadsPanel;
window.requestThreadsList = requestThreadsList;
window.createNewChat = createNewChat;

// UI State Management Functions
function saveUIState() {
    try {
        // Save scroll position
        uiState.scrollPosition = chatContainer.scrollTop;

        // Save expanded code blocks
        uiState.expandedCodeBlocks.clear();
        document.querySelectorAll('.code-block-content.expanded').forEach(content => {
            const id = content.id.replace('content-', '');
            uiState.expandedCodeBlocks.add(id);
        });

        // Save files changed state
        const filesContent = document.getElementById('filesChangedContent');
        uiState.filesChangedExpanded = filesContent && filesContent.classList.contains('expanded');

        // Store in sessionStorage for persistence
        sessionStorage.setItem('augura-ui-state', JSON.stringify({
            scrollPosition: uiState.scrollPosition,
            expandedCodeBlocks: Array.from(uiState.expandedCodeBlocks),
            filesChangedExpanded: uiState.filesChangedExpanded
        }));
    } catch (error) {
        console.warn('Failed to save UI state:', error);
    }
}

function restoreUIState() {
    try {
        const saved = sessionStorage.getItem('augura-ui-state');
        if (saved) {
            const state = JSON.parse(saved);

            // Restore scroll position
            if (state.scrollPosition) {
                setTimeout(() => {
                    chatContainer.scrollTop = state.scrollPosition;
                }, 100);
            }

            // Restore expanded code blocks
            if (state.expandedCodeBlocks) {
                uiState.expandedCodeBlocks = new Set(state.expandedCodeBlocks);
                state.expandedCodeBlocks.forEach(id => {
                    setTimeout(() => {
                        const content = document.getElementById('content-' + id);
                        const toggle = document.getElementById('toggle-' + id);
                        if (content && toggle) {
                            content.classList.add('expanded');
                            content.classList.remove('collapsed');
                            toggle.classList.remove('collapsed');
                            toggle.classList.remove('codicon-chevron-right');
                            toggle.classList.add('codicon-chevron-down');
                        }
                    }, 200);
                });
            }

            // Restore files changed state
            if (state.filesChangedExpanded) {
                uiState.filesChangedExpanded = true;
                setTimeout(() => {
                    const content = document.getElementById('filesChangedContent');
                    const toggle = document.getElementById('filesToggle');
                    if (content && toggle) {
                        content.classList.add('expanded');
                        content.classList.remove('collapsed');
                        toggle.classList.remove('collapsed');
                        toggle.classList.remove('codicon-chevron-right');
                        toggle.classList.add('codicon-chevron-down');
                    }
                }, 200);
            }

            // Update scroll button position after restoring files changed state
            setTimeout(() => {
                updateScrollButtonPosition();
            }, 250);
        }
    } catch (error) {
        console.warn('Failed to restore UI state:', error);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Update scroll button position on load
    setTimeout(() => {
        updateScrollButtonPosition();
    }, 100);
});
