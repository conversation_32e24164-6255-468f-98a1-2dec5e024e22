/**
 * Test script to verify the new file discovery improvements
 */

const { MCPTools } = require('./utils/mcp-tools');

async function testFileDiscovery() {
    console.log('🧪 Testing Augura File Discovery Improvements...\n');
    
    try {
        const mcpTools = new MCPTools();
        
        // Test 1: Get workspace context
        console.log('📋 Test 1: Getting workspace context...');
        const context = await mcpTools.getWorkspaceContext();
        if (context) {
            console.log('✅ Workspace context retrieved successfully');
            console.log(`   - Workspace root: ${context.workspaceRoot}`);
            console.log(`   - Open files: ${context.openFiles?.length || 0}`);
            console.log(`   - Project structure: ${context.projectStructure?.success ? 'Available' : 'Not available'}`);
            console.log(`   - All files: ${context.allFiles?.success ? context.allFiles.files.length + ' files' : 'Not available'}`);
        } else {
            console.log('❌ Failed to get workspace context');
        }
        
        console.log('\n' + '─'.repeat(50) + '\n');
        
        // Test 2: Get project structure
        console.log('🌳 Test 2: Getting project structure...');
        const structure = await mcpTools.getProjectStructure();
        if (structure.success) {
            console.log('✅ Project structure retrieved successfully');
            console.log('   Structure preview:');
            console.log(`   📁 ${structure.structure.name}`);
            if (structure.structure.children) {
                structure.structure.children.slice(0, 5).forEach(child => {
                    const icon = child.type === 'directory' ? '📁' : '📄';
                    console.log(`     ${icon} ${child.name}`);
                });
                if (structure.structure.children.length > 5) {
                    console.log(`     ... and ${structure.structure.children.length - 5} more items`);
                }
            }
        } else {
            console.log(`❌ Failed to get project structure: ${structure.error}`);
        }
        
        console.log('\n' + '─'.repeat(50) + '\n');
        
        // Test 3: Get all project files
        console.log('📂 Test 3: Getting all project files...');
        const allFiles = await mcpTools.getAllProjectFiles();
        if (allFiles.success) {
            console.log(`✅ Found ${allFiles.files.length} project files`);
            
            // Group by type
            const filesByType = {};
            allFiles.files.forEach(file => {
                if (!filesByType[file.type]) {
                    filesByType[file.type] = [];
                }
                filesByType[file.type].push(file);
            });
            
            console.log('   Files by type:');
            Object.keys(filesByType).forEach(type => {
                console.log(`     ${type}: ${filesByType[type].length} files`);
            });
        } else {
            console.log(`❌ Failed to get project files: ${allFiles.error}`);
        }
        
        console.log('\n' + '─'.repeat(50) + '\n');
        
        // Test 4: Test file filtering
        console.log('🔍 Test 4: Testing file filtering...');
        const testFiles = [
            'index.js',
            'package.json',
            'node_modules/test.js',
            '.git/config',
            'README.md',
            'dist/bundle.js',
            'src/components/App.jsx'
        ];
        
        console.log('   Testing shouldIgnoreItem:');
        testFiles.forEach(file => {
            const shouldIgnore = mcpTools.shouldIgnoreItem(file);
            const status = shouldIgnore ? '❌ Ignored' : '✅ Included';
            console.log(`     ${file}: ${status}`);
        });
        
        console.log('\n   Testing isRelevantFile:');
        testFiles.forEach(file => {
            const isRelevant = mcpTools.isRelevantFile(file);
            const status = isRelevant ? '✅ Relevant' : '❌ Not relevant';
            console.log(`     ${file}: ${status}`);
        });
        
        console.log('\n🎉 All tests completed successfully!');
        console.log('\n📝 Summary of improvements:');
        console.log('   ✅ Enhanced workspace context with complete project visibility');
        console.log('   ✅ Intelligent file filtering to focus on relevant files');
        console.log('   ✅ Project structure analysis and visualization');
        console.log('   ✅ File type classification and organization');
        console.log('   ✅ Smart discovery of all project files');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    testFileDiscovery();
}

module.exports = { testFileDiscovery };
