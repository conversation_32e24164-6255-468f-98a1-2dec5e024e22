# 🤖 Augura

Advanced AI-powered coding agent for Visual Studio Code using MCP (Model Context Protocol) and Groq API.

## ✨ Features

- 🤖 **Intelligent Agent Mode**: Not just a chatbot - an actual coding agent that can take actions
- ⚡ **Ultra-Fast**: Powered by Gro<PERSON>'s lightning-fast API with streaming responses
- 📁 **File System Access**: Read, write, create, and modify files in your workspace
- 🚀 **Code Generation**: Generate code from natural language descriptions
- 📊 **Project Analysis**: Understand your entire project structure
- 🎯 **Context Aware**: Maintains context of your workspace and current tasks

## 🏗️ Architecture

### Core Components

- **Extension.js**: Main VS Code extension entry point
- **AI Agent**: Intelligent agent with Groq API integration
- **MCP Tools**: Model Context Protocol tools for VS Code integration
- **Chat Interface**: Advanced web-based UI with agent capabilities

### Technologies Used

- **Backend**: Node.js, VS Code Extension API
- **AI**: Groq API with `moonshotai/kimi-k2-instruct` model
- **Frontend**: HTML5, CSS3, JavaScript
- **Protocol**: MCP (Model Context Protocol)
- **Streaming**: Real-time response streaming
- **Syntax Highlighting**: Prism.js with 100+ languages

## 🚀 Quick Start

### Prerequisites

- Visual Studio Code 1.74.0 or higher
- Node.js 18.0 or higher

### Installation

1. **Clone or download this project**
   ```bash
   git clone https://github.com/augura/augura.git
   cd augura
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Open in VS Code**
   ```bash
   code .
   ```

4. **Run the extension**
   - Press `F5` to open a new Extension Development Host window
   - The extension will be automatically loaded

### Configuration

The extension works out of the box with a default Groq API key. For production use, configure your own:

1. Open VS Code Settings (`Ctrl+,`)
2. Search for "Augura"
3. Set your Groq API key in `augura.groqApiKey`

## 🎯 Usage

### Commands

- **`Ctrl+Alt+A`**: Open Augura Chat
- **`Ctrl+Alt+G`**: Generate code from description

### Context Menu

Right-click in any file to access Augura commands through the command palette.

### Agent Capabilities

The AI Agent can:

1. **Read Files**: Access and analyze any file in your workspace
2. **Write Files**: Create new files or modify existing ones
3. **Execute Commands**: Run VS Code commands and operations
4. **Analyze Projects**: Understand your project structure and dependencies
5. **Track Changes**: Monitor and display file modifications
6. **Provide Context**: Maintain awareness of your current work

### Example Interactions

```
User: "Analyze my main.js file and fix any issues"
Agent: 🔍 Reading file: main.js
        📊 Found 3 potential issues
        🔧 Fixing syntax error on line 15
        ✅ File updated successfully

User: "Create a new React component for user authentication"
Agent: 🚀 Generating React component...
        📄 Created file: components/UserAuth.jsx
        📝 Added TypeScript definitions
        ✅ Component ready for use
```

## 🔧 Configuration Options

```json
{
  "augura.groqApiKey": "your-api-key-here",
  "augura.model": "moonshotai/kimi-k2-instruct",
  "augura.autoAnalyze": true,
  "augura.streamResponse": true
}
```

### Settings

- **`groqApiKey`**: Your Groq API key (optional - uses default if empty)
- **`model`**: AI model to use (default: moonshotai/kimi-k2-instruct)
- **`autoAnalyze`**: Automatically analyze files when opened
- **`streamResponse`**: Enable real-time streaming responses

## 🛠️ Development

### Project Structure

```
augura/
├── extension.js              # Main extension file
├── package.json             # Extension manifest
├── utils/
│   ├── ai-agent.js         # AI agent implementation
│   └── mcp-tools.js        # MCP tools for VS Code
└── templates/
    ├── chat.html           # Chat interface
    ├── script/
    │   └── script.js       # Frontend JavaScript
    └── styles/
        └── style.css       # UI styles
```

### Key Features Implementation

1. **MCP Integration**: Full Model Context Protocol support
2. **Streaming Responses**: Real-time AI response streaming
3. **File Operations**: Complete file system access
4. **Agent Actions**: Visual feedback for agent operations
5. **Task Tracking**: Current task display and progress
6. **Change Monitoring**: File modification tracking

### API Integration

The extension uses Groq API with the following configuration:

```javascript
const response = await axios.post('https://api.groq.com/openai/v1/chat/completions', {
    model: 'moonshotai/kimi-k2-instruct',
    messages: messages,
    stream: true,
    temperature: 0.7,
    max_tokens: 2048
}, {
    headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
    }
});
```

## 🔒 Security

- API keys are stored securely in VS Code settings
- File operations are restricted to workspace folders
- All user inputs are sanitized
- No data is collected or shared externally

## 🐛 Troubleshooting

### Common Issues

1. **Extension not loading**
   - Check VS Code version (requires 1.74.0+)
   - Verify Node.js installation
   - Run `npm install` in project directory

2. **API errors**
   - Verify internet connection
   - Check Groq API key configuration
   - Monitor API rate limits

3. **File access issues**
   - Ensure workspace is properly opened
   - Check file permissions
   - Verify file paths are correct

### Debug Mode

Enable debug logging by setting:
```json
{
  "augura.debug": true
}
```

## 📝 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For issues and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review VS Code extension documentation

---

**Built with ❤️ using VS Code Extension API, MCP, and Groq AI**
